const prisma = require('../src/database/prisma/getPrismaClient');
require('dotenv').config();

async function cleanupAwsReferences() {
  console.log('🧹 Starting AWS reference cleanup...');
  
  try {
    // Update Configurations table
    console.log('📊 Cleaning up Configurations table...');
    const configs = await prisma.configurations.findMany();
    let configUpdatedCount = 0;
    
    for (const config of configs) {
      const updates = {};
      let hasUpdates = false;
      
      // Clean up scraperApi config
      if (config.scraperApi) {
        let scraperApi = { ...config.scraperApi };
        let scraperApiUpdated = false;
        
        // Remove AWS-specific fields
        const awsFields = ['awsRegion', 'awsAccessKey', 'awsSecretKey', 'awsInstanceIds'];
        awsFields.forEach(field => {
          if (scraperApi[field]) {
            delete scraperApi[field];
            scraperApiUpdated = true;
            console.log(`  ✅ Configuration ${config.id}: Removed ${field}`);
          }
        });
        
        // Add Azure equivalents
        if (!scraperApi.azureLocation) {
          scraperApi.azureLocation = 'Central India';
          scraperApiUpdated = true;
        }
        
        if (!scraperApi.resourceGroup && process.env.RESOURCE_GROUP_NAME) {
          scraperApi.resourceGroup = process.env.RESOURCE_GROUP_NAME;
          scraperApiUpdated = true;
        }
        
        if (scraperApiUpdated) {
          updates.scraperApi = scraperApi;
          hasUpdates = true;
        }
      }
      
      // Clean up jungleScout config
      if (config.jungleScout) {
        let jungleScout = { ...config.jungleScout };
        let jungleScoutUpdated = false;
        
        // Remove AWS-specific fields
        if (jungleScout.awsRegion) {
          delete jungleScout.awsRegion;
          jungleScout.azureLocation = 'Central India';
          jungleScoutUpdated = true;
          console.log(`  ✅ Configuration ${config.id}: Updated JungleScout region`);
        }
        
        if (jungleScoutUpdated) {
          updates.jungleScout = jungleScout;
          hasUpdates = true;
        }
      }
      
      // Add Azure configuration if not exists
      if (!config.azureConfig) {
        updates.azureConfig = {
          resourceGroup: process.env.RESOURCE_GROUP_NAME || 'your-resource-group',
          subscriptionId: process.env.AZURE_SUBSCRIPTION_ID || 'your-subscription-id',
          location: 'Central India',
          storageType: 'aws-s3',
          bucketName: 'eq--assets',
          availableVMs: ['jeff-worker-1', 'jeff-worker-2', 'jeff-worker-3', 'jeff-worker-4']
        };
        hasUpdates = true;
        console.log(`  ✅ Configuration ${config.id}: Added Azure configuration`);
      }
      
      if (hasUpdates) {
        await prisma.configurations.update({
          where: { id: config.id },
          data: updates
        });
        configUpdatedCount++;
      }
    }
    
    console.log(`📊 Updated ${configUpdatedCount} configurations`);
    
    // Clean up JobCentral table
    console.log('📊 Cleaning up JobCentral table...');
    const jobs = await prisma.jobCentral.findMany({
      where: {
        OR: [
          { config: { path: ['awsRegion'], not: null } },
          { config: { path: ['awsInstanceIds'], not: null } },
          { config: { path: ['awsAccessKey'], not: null } }
        ]
      }
    });
    
    let jobUpdatedCount = 0;
    for (const job of jobs) {
      let config = { ...job.config };
      let updated = false;
      
      // Remove AWS-specific fields
      const awsFields = ['awsRegion', 'awsAccessKey', 'awsSecretKey', 'awsInstanceIds'];
      awsFields.forEach(field => {
        if (config[field]) {
          delete config[field];
          updated = true;
          console.log(`  ✅ Job ${job.id}: Removed ${field}`);
        }
      });
      
      // Add Azure equivalents
      if (!config.azureLocation) {
        config.azureLocation = 'Central India';
        updated = true;
      }
      
      if (!config.resourceGroup && process.env.RESOURCE_GROUP_NAME) {
        config.resourceGroup = process.env.RESOURCE_GROUP_NAME;
        updated = true;
      }
      
      if (updated) {
        await prisma.jobCentral.update({
          where: { id: job.id },
          data: { config }
        });
        jobUpdatedCount++;
      }
    }
    
    console.log(`📊 Updated ${jobUpdatedCount} job configurations`);
    
    // Search for hardcoded AWS references in text fields
    console.log('🔍 Searching for hardcoded AWS references...');
    
    // Common AWS patterns to look for
    const awsPatterns = [
      /ap-south-1/g,
      /us-east-1/g,
      /us-west-2/g,
      /eu-west-1/g,
      /amazonaws\.com/g,
      /s3\.amazonaws/g,
      /ec2\.amazonaws/g
    ];
    
    // Check all text and JSON fields for AWS references
    const tables = ['User', 'JobCentral', 'Configurations'];
    let totalReferencesFound = 0;
    
    for (const tableName of tables) {
      console.log(`  🔍 Checking ${tableName} table...`);
      
      // This is a simplified check - in practice, you'd want to be more specific
      // about which fields to check based on your actual schema
      const records = await prisma[tableName.toLowerCase()].findMany();
      
      for (const record of records) {
        const recordStr = JSON.stringify(record);
        let foundReferences = false;
        
        awsPatterns.forEach(pattern => {
          if (recordStr.match(pattern)) {
            foundReferences = true;
            totalReferencesFound++;
          }
        });
        
        if (foundReferences) {
          console.log(`    ⚠️  Found AWS references in ${tableName} record ${record.id}`);
        }
      }
    }
    
    if (totalReferencesFound > 0) {
      console.log(`⚠️  Found ${totalReferencesFound} records with potential AWS references`);
      console.log('   These may need manual review and updating');
    } else {
      console.log('✅ No additional AWS references found');
    }
    
    // Summary
    console.log('\n📋 Cleanup Summary:');
    console.log(`  • Configurations cleaned: ${configUpdatedCount}`);
    console.log(`  • Jobs cleaned: ${jobUpdatedCount}`);
    console.log(`  • AWS references found: ${totalReferencesFound}`);
    console.log('\n✅ AWS reference cleanup completed successfully!');
    
    // Show Azure configuration added
    console.log('\n🔧 Azure Configuration Added:');
    console.log(`  • Resource Group: ${process.env.RESOURCE_GROUP_NAME || 'your-resource-group'}`);
    console.log(`  • Subscription ID: ${process.env.AZURE_SUBSCRIPTION_ID || 'your-subscription-id'}`);
    console.log(`  • Location: Central India`);
    console.log(`  • Storage: AWS S3 (eq--assets bucket)`);
    console.log(`  • Note: Keeping AWS S3 for storage operations`);
    
  } catch (error) {
    console.error('❌ Error during cleanup:', error);
    throw error;
  }
}

// Run the cleanup if this script is executed directly
if (require.main === module) {
  cleanupAwsReferences()
    .catch(console.error)
    .finally(() => prisma.$disconnect());
}

module.exports = { cleanupAwsReferences };
