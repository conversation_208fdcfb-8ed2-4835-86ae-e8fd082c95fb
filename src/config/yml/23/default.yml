bulletPointReport:
  auditRank: 1
  noOfReviews: 2
  dataPoint: "Bullet Points"
  conditions:
    - store:
        bulletPointCounts: "bulletPoints.Points.length || 0"
        totalCharacters: "bulletPoints.TotalChars"
        allCapsCheck: "bulletPoints.isItAllCaps"
        isLongBulletPoint: "(point, index) => point.NumberChars > 270 ? index + 1 : null"
        isShortBulletPoint: "(point, index) => point.NumberChars < 130 ? index + 1 : null"
        longPoints: "bulletPoints.Points.map(isLongBulletPoint).filter(i=>i)"
        shortPoints: "bulletPoints.Points.map(isShortBulletPoint).filter(i=>i)"
        formattedExample: "getFormattedText(bulletPoints.Points.find(point => point.FirstWordCapital)?.value || '')"
        allShortPoints: "shortPoints.length === bulletPoints.Points.length"
        allLongPoints: "longPoints.length === bulletPoints.Points.length"
        indexedLengthPoints: "bulletPoints.Points.map((point, index) => ({index: index + 1, length: point.NumberChars})).sort((a, b) => a.length - b.length)"
        maxBulletPointIndex: "indexedLengthPoints.at(-1)?.index"
        minBulletPointIndex: "indexedLengthPoints?.[0]?.index"
        minBulletPointChars: "indexedLengthPoints?.[0]?.length"
        maxBulletPointChars: "indexedLengthPoints.at(-1)?.length"
        joinedLongPoints: "longPoints.join(', ')"
        joinedShortPoints: "shortPoints.join(', ')"
        moreThanOneShortPoint: "shortPoints.length > 1"
        moreThanOneLongPoint: "longPoints.length > 1"
        hasOrHave: "moreThanOneShortPoint ? 'have' : 'has'"
        longHasOrHave: "moreThanOneLongPoint ? 'have' : 'has'"
        longBulletPointText: "longPoints.length>1 ?`s:` : ``"
        shortBulletPointText: "shortPoints.length>1 ?`s:` : ``"

    - type: lessThanFiveBulletPoints
      auditRank: 1
      when: "bulletPointCounts > 0 && bulletPointCounts < 5"

    - type: longBulletPoints
      auditRank: 1
      when: "longPoints.length > 0 && longPoints.length > shortPoints.length && !allLongPoints"

    - type: allLongBulletPoints
      auditRank: 1
      when: "bulletPointCounts > 0 && allLongPoints"

    - type: shortBulletPoints
      auditRank: 1
      when: "shortPoints.length > 0 && longPoints.length < shortPoints.length && !allShortPoints"
      store:
        joinedShortPoints: "shortPoints.join(', ')"

    - type: allShortBulletPoints
      auditRank: 1
      when: "shortPoints.length > 0 && allShortPoints"

    - type: allCaps
      auditRank: 1
      when: "bulletPointCounts > 0 && allCapsCheck"

    - type: noBulletPoints
      auditRank: 1
      when: "bulletPointCounts <= 0"

  lessThanFiveBulletPoints:
    priority: "Medium"
    logic: "< 5 Bullet Points"
    painPoint: "You have only have {{bulletPointCounts}} bullet points."
    improvements:
      - "You're not utilizing the full SEO potential of bullet points.Make sure to have at least 5 bullet points."
    benefits:
      - "CVR ↑"
      - "Visibility & SEO ↑"
      - "Organic Rankings ↑"

  longBulletPoints:
    priority: "Medium"
    logic: "The bullet points have more than 250 characters"
    painPoint: "Bullet Point{{longBulletPointText}} {{joinedLongPoints}} {{longHasOrHave}} more than 250 characters."
    improvements:
      - "Decrease the length of long bullet points. The ideal length is 150-200 characters. Bullet Point {{maxBulletPointIndex}} has {{maxBulletPointChars}} characters."
    benefits:
      - "CVR ↑"
      - "Visibility & SEO ↑"
      - "Organic Rankings ↑"

  shortBulletPoints:
    priority: "Medium"
    logic: "Bullet points have less than 150 Characters"
    painPoint: "Bullet Point{{shortBulletPointText}} {{joinedShortPoints}} {{hasOrHave}} less than 150 characters."
    improvements:
      - "Increase the length of short bullet point. The ideal length is 150-200 characters. Bullet Point {{minBulletPointIndex}} has {{minBulletPointChars}} characters."
    benefits:
      - "CVR ↑"
      - "Visibility & SEO ↑"
      - "Organic Rankings ↑"

  allCaps:
    priority: "Medium"
    logic: "The bullet points are in all caps"
    painPoint: "The bullet points are in all capital letters"
    improvements:
      - "Change them to sentence case like - {{formattedExample}}"
    benefits:
      - "CVR ↑"

  noBulletPoints:
    priority: "Medium"
    logic: "< 5 Bullet Points"
    painPoint: "There are no bullet points."
    improvements:
      - "Add at least 5 bullet points to utilize the full SEO potential."
    benefits:
      - "CVR ↑"
      - "Visibility & SEO ↑"
      - "Organic Rankings ↑"

  allLongBulletPoints:
    logic: "Bullet points have more than 250 chars"
    painPoint: "All bullet points have more than 250 characters."
    improvements:
      - "Decrease the length of long bullet points. The ideal length is 150-200 characters. Bullet Point {{maxBulletPointIndex}} has {{maxBulletPointChars}} characters."
    benefits:
      - "CVR ↑"
      - "Visibility & SEO ↑"
      - "Organic Rankings ↑"

  allShortBulletPoints:
    logic: "Bullet points have less than 150 Chars"
    painPoint: "All bullet points have less than 150 characters."
    improvements:
      - "Increase the length of short bullet points. The ideal length is 150-200 characters. Bullet Point {{minBulletPointIndex}} has {{minBulletPointChars}} characters."
    benefits:
      - "CVR ↑"
      - "Visibility & SEO ↑"
      - "Organic Rankings ↑"

productTitleReport:
  auditRank: 1
  dataPoint: "Product Title"
  conditions:
    - store:
        guidelines: "titleLength > 100 ? titleLengthGuidelines['Default'] : titleLengthGuidelines[category]"

    - type: shortTitleWithoutGuidelines
      auditRank: 1
      when: "!guidelines && titleLength <= 80"

    - type: averageTitleWithoutGuidelines
      auditRank: 1
      when: "!guidelines && titleLength > 150 && titleLength <= 180"
      
    - type: badTitle
      auditRank: 1
      when: "guidelines && titleLength <= guidelines.bad[1]"

    - type: averageTitleLessThan
      auditRank: 1
      when: "guidelines && titleLength > guidelines.bad[1] && titleLength <= guidelines.average[1]"

  shortTitleWithoutGuidelines:
    priority: "High"
    logic: "0-80"
    painPoint: "The title character length is {{titleLength}} which is causing you to miss a lot of traffic opportunities."
    improvements:
      - "Take advantage of the 200 character limit, ideally with 80-150 characters, to maximize SEO impact"
      - "Add more relevant & highly searched keywords."
      - "Make sure the main keywords are at the beginning."
    benefits:
      - "CVR ↑"
      - "CTR ↑"
      - "Visibility & SEO ↑"
      - "Organic Rankings ↑"
      - "Indexing"

  averageTitleWithoutGuidelines:
    priority: "Medium"
    logic: "150-180"
    painPoint: "The title character length is {{titleLength}} which is causing you to miss some traffic opportunities."
    improvements:
      - "Add relevant & high search volume keywords to improve SEO."
      - "Move the main keywords within the first 80 characters."
    benefits:
      - "CVR ↑"
      - "CTR ↑"
      - "Visibility & SEO ↑"
      - "Organic Rankings ↑"

  averageTitleLessThan:
    priority: "Medium"
    logic: "{{guidelines ? JSON.stringify(guidelines.average):'150-180'}}"
    painPoint: "Your title character length is {{titleLength}} which is causing you to miss some traffic opportunities."
    improvements:
      - "Take advantage of the full 200 character limit to maximize SEO impact"
      - "Add lots of relevant & highly searched keywords."
      - "Move the main keyword to the beginning."

    benefits:
      - "CVR ↑"
      - "CTR ↑"
      - "Visibility & SEO ↑"
      - "Organic Rankings ↑"

  badTitle:
    priority: "High"
    logic: "{{guidelines ? JSON.stringify(guidelines.bad) : '0-150'}}"
    painPoint: "Your title character length is {{titleLength}} which is causing you to miss a lot of traffic opportunities."
    improvements:
      - "Take advantage of the full 200 character limit to maximize SEO impact"
      - "Move the main keywords within the first 80 characters."

    benefits:
      - "CVR ↑"
      - "CTR ↑"
      - "Visibility & SEO ↑"
      - "Organic Rankings ↑"
      - "Indexing"

aPlusContentReport:
  auditRank: 1
  dataPoint: "A+ Content"
  conditions:
    - store:
        contentData: "data.productData[0].AplusContent"
        prospectRevenue: "Math.round(data.productData[0].price * data.productData[0].sales)"
        revenueIncrease: "Math.round(0.12 * prospectRevenue)"
        hasAPlusContent: "contentData.aplusContentPresent"
        hasAltText: "contentData.allImagesHaveAltText"
        productDescriptionLength: "data.productData[0].textDescription.numOfChars"
        hasBrandStory: "contentData.brandStory?.brandStoryPresent"
        hasPremiumAplus: "contentData?.premiumAPlusPresent"

    - type: notPresent
      auditRank: 1
      when: "!hasAPlusContent && prospectRevenue>0"

    - type: notPresentZeroProspectRevenue
      auditRank: 1
      when: "!hasAPlusContent && prospectRevenue==0"

    - type: shortDescriptionNonZero
      auditRank: 1
      when: "0 < productDescriptionLength && productDescriptionLength < 500"

    - type: noAltText
      auditRank: 1
      when: "!hasAltText && hasAPlusContent"

    - type: noPremiumAplus
      auditRank: 1
      when: "hasAPlusContent && !hasPremiumAplus"

    - type: noBrandStory
      auditRank: 1
      when: "!hasBrandStory"

  notPresent:
    priority: "Urgent"
    logic: "Not Present"
    painPoint: "There is no A+ content on the listing"
    improvements:
      - "Add A+ content to boost the algorithm. A+ content can increase sales by up to 12%. That’s an increase of  {{revenueIncrease}} per month just for this listing."
      - "Also, include Alt-text in the images."
    benefits:
      - "CVR ↑"
      - "Visibility & SEO ↑"

  notPresentZeroProspectRevenue:
    priority: "Urgent"
    logic: "Not Present"
    painPoint: "There is no A+ content on the listing"
    improvements:
      - "Add A+ content to boost the algorithm. A+ content can increase sales by up to 12%. That’s an increase of  {{revenueIncrease}} per month just for this listing."
      - "Also, include Alt-text in the images."
    benefits:
      - "CVR ↑"
      - "Visibility & SEO ↑"

  shortDescriptionNonZero:
    priority: "Medium"
    logic: "<500+ characters"
    painPoint: "The text description has {{productDescriptionLength}} characters, which is less than recommended."
    improvements:
      - "Increase character count to 500+ characters by adding more information about your product in the text description section."
    benefits:
      - "CVR ↑"
      - "Visibility & SEO ↑"

  noAltText:
    priority: "High"
    logic: "Alt Text is not there"
    painPoint: "The images do not have Alt text"
    improvements:
      - "Add alt-text with relevant keywords. This has a positive impact on the algorithm and improves the listing's SEO"
    benefits:
      - "Indexing"
      - "Organic Rankings ↑"

  noPremiumAplus:
    priority: "Medium"
    logic: "A+ content is present but premium A+ content not present"
    painPoint: "Premium A+ content is not utilized."
    improvements:
      - "Premium A+ content can increase your conversion rate and boost sales by 20%. It is free. Take advantage of this feature."
    benefits:
      - "CVR ↑"
      - "Sales ↑"

  noBrandStory:
    priority: "Medium"
    logic: "Brand story not present"
    painPoint: "There is no Brand Story"
    improvements:
      - "Add a brand story to help customers connect with your brand and improve conversion rates."
    benefits:
      - "CVR ↑"
      - "Brand Loyalty ↑"

storefrontReport:
  auditRank: 1
  dataPoint: "Storefront"
  conditions:
    - type: notPresent
      auditRank: 1
      when: "!storefrontStatus"

  notPresent:
    priority: "High"
    logic: "Not Present"
    painPoint: "I don't see a storefront present right now."
    improvements:
      - "You should make a storefront instantly. Include your best sellers at the top."
      - "Get a designer to make it well and according to your brand aesthetic."
      - "Include the “follow tag” so that customers get notified about new launches."
    benefits:
      - "CVR ↑"
      - "Algorithm boost"
      - "Visibility ↑"

ratingsReport:
  auditRank: 1
  dataPoint: "Ratings"

  conditions:

    - type: belowFourWithLowStarReviews
      auditRank: 1
      when: "rating < 4 && lowStarReviews > 0"

    - type: belowFourNoLowStarReviews
      auditRank: 1
      when: "rating < 4 && lowStarReviews == 0"

    - type: belowFourPointThreeWithLowStarReviews
      auditRank: 1
      when: "rating >= 4 && rating < 4.3 && lowStarReviews > 0"

    - type: belowFourPointThreeZeroWithNoLowStarReviews
      auditRank: 1
      when: "rating >= 4 && rating < 4.3 && lowStarReviews == 0"

    - type: belowFourPointFiveWithLowStarReviews
      auditRank: 1
      when: "rating >= 4.3 && rating < 4.5 && lowStarReviews > 0"

    - type: belowFourPointFiveZeroWithNoLowStarReviews
      auditRank: 1
      when: "rating >= 4.3 && rating < 4.5 && lowStarReviews == 0"

    - type: belowFourPointSevenWithLowStarReviews
      auditRank: 1
      when: "rating >= 4.5 && rating < 4.7 && lowStarReviews > 0"

    - type: aboveFourPointSeven
      auditRank: 1
      when: "rating >= 4.7"

  belowFourWithLowStarReviews:
    priority: "High"
    logic: "below 4"
    painPoint: "The star rating is {{rating}}, which could be improved to increase conversions"
    improvements:
      - "The ratings are shown as {{ratingComesAs}} stars on the search page, which makes it hard to compete against higher rated products."
      - "Understand consistencies and patterns in the low reviews and address it either through product updates or with callouts in the listing. Focus on driving more positive reviews."

    benefits:
      - "CVR ↑"
      - "CTR ↑"
      - "Algorithm boost"

  belowFourNoLowStarReviews:
    priority: "High"
    logic: "below 4"
    painPoint: "The product has a lower star rating of {{rating}}"
    improvements:
      - "The ratings are shown as {{ratingComesAs}} stars on the search page, which makes it hard to compete against higher rated products."
      - "Understand consistencies and patterns in the low reviews and address it either through product updates or with callouts in the listing. Focus on driving more positive reviews."

    benefits:
      - "CVR ↑"
      - "CTR ↑"
      - "Algorithm boost"

  belowFourPointThreeWithLowStarReviews:
    priority: "High"
    logic: "4 - 4.2"
    painPoint: "The product has a {{rating}} star rating. While the rating is good, it is within the threshold to be rounded down within the search results visual"
    improvements:
      - "On the search page your ratings are shown as 4 stars, limiting the ability to rank #1. Increasing the rating to 4.3 will make it show up as 4.5 stars."
      - "Understand consistencies and patterns in the low reviews and address it either through product updates or with callouts in the listing. Focus on driving more positive reviews."

    benefits:
      - "CVR ↑"
      - "CTR ↑"
      - "Algorithm boost"

  belowFourPointThreeZeroWithNoLowStarReviews:
    priority: "High"
    logic: "4 - 4.2"
    painPoint: "The product has a {{rating}} star rating. While the rating is good, it is within the threshold to be rounded down within the search result visual"
    improvements:
      - "On the search page your ratings are shown as 4 stars, limiting the ability to rank #1. Increasing the rating to 4.3 will make it show up as 4.5 stars."
      - "Understand consistencies and patterns in the low reviews and address it either through product updates or with callouts in the listing. Focus on driving more positive reviews."

    benefits:
      - "CVR ↑"
      - "CTR ↑"
      - "Algorithm boost"

  belowFourPointFiveWithLowStarReviews:
    priority: "High"
    logic: "4.3 - 4.5"
    painPoint: "The product has a {{rating}} star rating and is displaying as 4.5 on the search results page. While the rating is good, there’s opportunity to increase it to 4.8 to display as 5 stars"
    improvements:
      - "On the search page your ratings are shown as 4.5 stars. Increasing the rating to 4.8 will make it show up as 5 stars, which can help stand out and drive conversion."
      - "To improve the ratings, understand consistencies and patterns in the reviews and address it either through product updates or with callouts in the listing. Focus on driving more positive reviews."

    benefits:
      - "CVR ↑"
      - "CTR ↑"
      - "Algorithm boost"

  belowFourPointFiveZeroWithNoLowStarReviews:
    priority: "High"
    logic: "4.3 - 4.5"
    painPoint: "The product has a {{rating}} star rating and is displaying as 4.5 on the search results page. While the rating is good, there’s opportunity to increase it to 4.8 to display as 5 stars"
    improvements:
      - "On the search page your ratings are shown as 4.5 stars. Increasing the rating to 4.8 will make it show up as 5 stars, which can help stand out and drive conversion."
      - "To improve the ratings, understand consistencies and patterns in the reviews and address it either through product updates or with callouts in the listing. Focus on driving more positive reviews."
    benefits:
      - "CVR ↑"
      - "CTR ↑"
      - "Algorithm boost"

  belowFourPointSevenWithLowStarReviews:
    priority: "High"
    logic: "4.5 - 4.7"
    painPoint: "The product has a {{rating}} star rating which is good. It can be further improved to 5 stars to drive increased conversions."
    improvements:
      - "On the search page your ratings are shown as 4.5 stars, limiting the ability to rank #1. Increasing the number to 4.7 will make it show up as 5 stars."
      - "You have {{lowStarReviews}} of 1* & 2* reviews. To improve the ratings, understand consistencies in the reviews and address it either through product updates or with callouts in the listing. Focus on driving more positive reviews."
    benefits:
      - "CVR ↑"
      - "CTR ↑"
      - "Algorithm boost"

  aboveFourPointSeven:
    priority: "High"
    logic: "4.8+"
    painPoint: "Great job! Your ratings are fantastic. Prioritize keeping them above 4.8 stars."
    improvements:
      - "If the ratings fall below 4.8 stars, it will be displayed as 4.5 stars on the Amazon search page, negatively impacting the CVR & CTR."
    benefits:
      - "CVR ↑"
      - "CTR ↑"

reviewReport:
  auditRank: 1
  dataPoint: "Reviews"
  conditions:
    - store:
        nextIdealNumber: "calculateNextIdealNumber(reviewCount)"
        lowStarReviewsText: "lowStarReviews > 0 ? `${lowStarReviews}, 1 & 2 star reviews` : `some bad reviews`"

    - type: lessThan30
      auditRank: 1
      when: "reviewCount < 30"

    - type: between30And70
      auditRank: 1
      when: "reviewCount >= 30 && reviewCount < 70"

    - type: moreThan70
      auditRank: 1
      when: "reviewCount >= 70"

  lessThan30:
    priority: "High"
    logic: "<30"
    painPoint: "There are only {{reviewCount}} reviews which could be impacting your conversions."
    improvements:
      - "While 70+ reviews are ideal for scaling, the first goal is 30+."
      - "A few options to generate reviews include enrolling in the vine program & inserting a review request card in the packaging."
      - "The right review funnel can drive up to a 4% review rate"
    benefits:
      - "CVR ↑"
      - "CTR ↑"

  between30And70:
    priority: "Medium"
    logic: "30-70"
    painPoint: "There are {{reviewCount}} reviews. This is a great start but could be improved to drive more conversions."
    improvements:
      - "For the most success, Target at least 70+ reviews before running ads."
      - "A few options to generate reviews include enrolling in the vine program & inserting a review request card in the packaging."
      - "The right review funnel can drive up to a 4% review rate"
    benefits:
      - "CVR ↑"
      - "CTR ↑"

  moreThan70:
    priority: "Low"
    logic: "70+"
    painPoint: "Great Job! I see you have {{reviewCount}} reviews but I also see {{lowStarReviewsText}}."
    improvements:
      - "Aim for {{nextIdealNumber}} reviews."
      - "Identify any trends in 1 & 2 star reviews and look for ways to address in the listing or through product updates."
      - "Option to identify any non compliant reviews and push to get them removed."

    benefits:
      - "CVR ↑"
      - "CTR ↑"

adsReport:
  auditRank: 1
  dataPoint: "Ads"
  conditions:
    - store:
        improvementMessage: "prospectRevenue >= 20 && prospectRevenue <= 80 ? 'Amazon ads perform best for products that are $20 - $80 (fitting your range).' : 'Our expertise is in running ads for expensive products which are $100+.'"

    - type: notPresent
      auditRank: 1
      when: "!adsStatus"

  notPresent:
    priority: "Urgent"
    logic: "Not Present"
    painPoint: "Was searching your company's name on Amazon from multiple US pincodes but didn't see any ads from you guys."
    improvements:
      - "You should instantly start running ads, they are the best way you can scale on Amazon."
      - "{{improvementMessage}}"
      - "At your stage of the business, I think you should start with Sponsored product ads."
      - "Make sure campaigns are structured properly."
    benefits:
      - "Profit ↑"
      - "Visibility & SEO ↑"

videoReport:
  auditRank: 1
  dataPoint: "Videos"
  conditions:
    - store:
        revenueIncrease: "Math.ceil(prospectRevenue * 0.097)"

    - type: noVideoWithRevenue
      auditRank: 1
      when: "videosCount === 0 && prospectRevenue>0"

    - type: noVideoWithoutRevenue
      auditRank: 1
      when: "videosCount === 0 && prospectRevenue==0"

  noVideoWithRevenue:
    priority: "Urgent"
    logic: "Video not present"
    painPoint: "There are no product videos on the listing"
    improvements:
      - "Add a video of the product that highlights the benefits & addresses pain points. Videos drive up to 9.7% lift in sales, which is an estimated ${{revenueIncrease}} per month for this listing."
    benefits:
      - "CVR ↑"
      - "Algorithm boost"

  noVideoWithoutRevenue:
    priority: "Urgent"
    logic: "Video not present"
    painPoint: "There are no product videos on the listing"
    improvements:
      - "Add a video of the product that highlights the benefits & addresses pain points. Videos drive up to 9.7% lift in sales."

    benefits:
      - "CVR ↑"
      - "Algorithm boost"

imageReport:
  auditRank: 1
  dataPoint: "Images"
  conditions:
    - store:
        hasVideo: "videosCount > 0"
        insufficientImages: "(0 < imagesCount && imagesCount < 6) || (imagesCount === 5 && hasVideo)"

    - type: insufficientImages
      auditRank: 1
      when: "insufficientImages"

    - type: noImages
      auditRank: 1
      when: "imagesCount == 0"

  insufficientImages:
    priority: "Urgent"
    logic: "< 6 images"
    painPoint: "There are only {{imagesCount}} images on the listing."
    improvements:
      - "For best results, there should be at least 6 images including different angles, lifestyle images & infographics."
      - "This helps the consumers better understand your product and leads to higher conversions."
    benefits:
      - "CVR ↑"

  noImages:
    priority: "Urgent"
    logic: "No images"
    painPoint: "There are no Images on your listing right now."
    improvements:
      - "For best results, there should be at least 6 images including different angles, lifestyle images & infographics."
      - "This helps the consumers better understand your product and leads to higher conversions."
    benefits:
      - "CVR ↑"
