# Lex Review Compliance Check Prompt
# This prompt analyzes reviews for compliance with Amazon community guidelines

- role: system
  content: "You are an Amazon review compliance analysis assistant."

- role: user
  content: |
    Analyze the following Amazon product review for compliance with community guidelines. For each guideline section, provide a confidence rating (0-3) indicating the likelihood of non-compliance:

    0 = No violation
    1 = Low confidence of violation (possibly non-compliant)
    2 = Medium confidence of violation (likely non-compliant)
    3 = High confidence of violation (clearly non-compliant)

    INPUTS:
    - Product Title: {{productTitle}}
    - Review Title: {{reviewTitle}}
    - Review Content: {{reviewContent}}
    - Review context: **{{reviewAnalysis}}**

    DETAILED GUIDELINE SECTIONS:

    G1 : Review focuses ONLY on Sellers and the Customer Service they provide
    ALLOWED : "The product was good but customer service could be better" 
    NOT ALLOWED : "Worst Customer service,nobody ever answers to mails" 
    REASONING: Community content should help customers learn about the product itself, not individual ordering experiences

    G2: Review focuses ONLY on Ordering issues and returns
    ALLOWED : "The product is great but one of the products were missing"
    NOT ALLOWED : "I received the wrong order and now I can't even return" 
    REASONING: Community content should help customers learn about the product itself, not individual ordering experiences

    G3: SHIPPING & PACKAGING
    ALLOWED : "The product is okay, could have been packed better"
    NOT ALLOWED: "Everything was spilling and damaged" "The product came broken, disappointed"
    REASONING: Community content should help customers learn about the product itself, not individual ordering experiences

    G4: PRODUCT CONDITION ON ARRIVAL: Review focuses ONLY on Product condition on arrival and damaged product at time of arrival
    ALLOWED: Comments about the product's performance, quality, strength and features, If the review talks about the product along with the seller/shipping feedback.
    NOT ALLOWED: "The box arrived damaged,"
    REASONING: Community content should help customers learn about the product itself, not individual ordering experience

    G5 :SHIPPING COST : Review focuses ONLY on Shipping cost and speed
    ALLOWED : Product is great, shipping could have been faster "
    NOT ALLOWED : "Seller took too long to ship,"
    REASONING: Community content should help customers learn about the product itself, not individual ordering experiences

    G6. PRICING/AVAILABILITY: Review contains individual pricing experiences or store-specific availability comments not relevant to all customers.
       ALLOWED: Value comments about the product (e.g., "For only $29, this blender is really great")
       ALLOWED: General availability wishes (e.g., "I wish this book was also available in paperback")
       NOT ALLOWED: Individual pricing experiences (e.g., "Found this item here for $5 less than at my local store")
       NOT ALLOWED: Store-specific availability (e.g., "My local Target doesn't carry this anymore")
       REASONING: These comments aren't relevant for all customers

    G7. LANGUAGE VIOLATION: Content is written in languages other than English or Spanish or mixes languages inappropriately.
       ALLOWED: Content in the supported languages of the Amazon site
       NOT ALLOWED: Content in unsupported languages or mixed-language content
       REASONING: Content must be accessible to all users of the site

    G8. SPAM/REPETITIVE CONTENT: Contains repetitive text, nonsense, gibberish, distracting punctuation/symbols, or ASCII art.
       ALLOWED: Coherent, non-repetitive text relevant to the product
       NOT ALLOWED: Repetitive text, nonsense/gibberish, content consisting mainly of punctuation/symbols, ASCII art
       REASONING: Such content is distracting and doesn't help customers make purchasing decisions

    G9. PRIVATE INFORMATION: Shares personal information.
       NOT ALLOWED: Phone numbers, email addresses, mailing addresses, license plates, DSN, order numbers
       REASONING: Protects privacy and prevents identity theft

    G10. PROFANITY/HARASSMENT: Contains profanity, obscenities, name-calling, harassment, threats, attacks, libel, defamation, or inflammatory content.
    ALLOWED: Questioning beliefs or expertise respectfully, "I hate the product" "The product is crap"
    NOT ALLOWED: Profanity/obscenities/name-calling, harassment/threats, attacks on people, libel/defamation/inflammatory content, coordinated posting from multiple accounts. Using Only abuse
    REASONING: Maintains a respectful community environment

    G11. HATE SPEECH: Expresses hatred based on protected characteristics or promotes organizations using hate speech.
       NOT ALLOWED: Hatred based on race, ethnicity, nationality, gender, gender identity, sexual orientation, religion, age, or disability
       NOT ALLOWED: Promoting organizations that use such hate speech
       REASONING: Ensures an inclusive and respectful environment

    G12. SEXUAL CONTENT: Contains inappropriate sexual content.
       ALLOWED: Discussing sex and sensuality products sold on Amazon or products with sexual content
       NOT ALLOWED: Profanity/obscene language, content with nudity, sexually explicit images or descriptions
       REASONING: Maintains appropriate content standards

    G13. EXTERNAL LINKS: Contains links to external sites, phishing, malware, or URLs with referrer/affiliate codes.
       ALLOWED: Links to other products on Amazon
       NOT ALLOWED: Links to external sites, phishing/malware sites, URLs with referrer tags or affiliate codes
       REASONING: Ensures user safety and prevents exploitation

    G14. PROMOTIONAL CONTENT: Primary purpose is promoting a company, website, or special offer, or was created by someone with financial interest.
       NOT ALLOWED: Content whose MAIN purpose is promotion of another brand or product.
       REASONING: Prevents conflicts of interest and ensures authentic reviews

    G15. ILLEGAL ACTIVITIES: Encourages illegal activities.
       NOT ALLOWED: Content encouraging violence, illegal drug use, underage drinking, child/animal abuse, fraud, terrorism
       NOT ALLOWED: Threats of physical/financial harm, fraudulent schemes, encouraging dangerous product misuse
       REASONING: Prevents promotion of harmful or illegal behavior

    G16. MEDICAL CLAIMS: Makes statements about preventing or curing serious medical conditions.
       NOT ALLOWED: Claims related to preventing/curing serious medical conditions for any product type (including foods, beverages, supplements, cosmetics, personal care products)
       REASONING: Prevents potentially dangerous medical misinformation

    Make sure not to hallucinate or generate content that is not there.

    OUTPUT FORMAT:

    REVIEW_INTERPRETATION: [Primary interpretation of the review's content and intent, noting any ambiguities or alternative readings]

    RATINGS:
    G1: [0-3]
    G2: [0-3]
    G3: [0-3]
    G4: [0-3]
    G5: [0-3]
    G6: [0-3]
    G7: [0-3]
    G8: [0-3]
    G9: [0-3]
    G10: [0-3]
    G11: [0-3]
    G12: [0-3]
    G13: [0-3]
    G14: [0-3]
    G15: [0-3]
    G16: [0-3]

    TOP_VIOLATIONS:
    [ONLY if any violation rating above is more than or equal to 2, share the details below, else, List the three highest-rated violations in descending order by rating]

    TOP1: G# - [Rating] - [Concise reasoning]
    TOP2: G# - [Rating] - [Concise reasoning]
    TOP3: G# - [Rating] - [Concise reasoning]

    Make sure TOP_VIOLATIONS output format is clean and single quoted only, for example:

    'TOP1: G10 - 2 - The review contains inflammatory content, including accusations of fraud ("absolute scam") and references to lawsuits, which could be considered defamatory.  \n' +
    'TOP2: G8 - 1 - The review includes some unclear or repetitive phrasing (e.g., "Consciences of the tubes"), which may slightly detract from clarity but does not constitute spam.  \n' +
    'TOP3: G6 - 1 - The review mentions pricing concerns but focuses on product value rather than individual pricing experiences.'
