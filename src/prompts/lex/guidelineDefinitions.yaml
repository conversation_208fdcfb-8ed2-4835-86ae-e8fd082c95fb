# src/prompts/lex/guidelineDefinitions.yaml
amazon_review_guidelines:
  description: "Amazon Community Guidelines for Product Reviews"
  version: "2024.1"
  
  guidelines:
    G1:
      title: "Seller & Customer Service Focus"
      description: "Review focuses ONLY on Sellers and the Customer Service they provide"
      allowed_example: "The product was good but customer service could be better"
      not_allowed_example: "Worst Customer service,nobody ever answers to mails"
      reasoning: "Community content should help customers learn about the product itself, not individual ordering experiences"
      
    G2:
      title: "Ordering Issues & Returns"
      description: "Review focuses ONLY on Ordering issues and returns"
      allowed_example: "The product is great but one of the products were missing"
      not_allowed_example: "I received the wrong order and now I can't even return"
      reasoning: "Community content should help customers learn about the product itself, not individual ordering experiences"
      
    G3:
      title: "Shipping & Packaging"
      description: "Review focuses ONLY on shipping and packaging issues"
      allowed_example: "The product is okay, could have been packed better"
      not_allowed_example: "Everything was spilling and damaged"
      reasoning: "Community content should help customers learn about the product itself, not individual ordering experiences"
      
    G4:
      title: "Product Condition on Arrival"
      description: "Review focuses ONLY on Product condition on arrival and damaged product at time of arrival"
      allowed_example: "Comments about the product's performance, quality, strength and features, If the review talks about the product along with the seller/shipping feedback"
      not_allowed_example: "The box arrived damaged"
      reasoning: "Community content should help customers learn about the product itself, not individual ordering experience"
      
    G5:
      title: "Shipping Cost & Speed"
      description: "Review focuses ONLY on Shipping cost and speed"
      allowed_example: "Product is great, shipping could have been faster"
      not_allowed_example: "Seller took too long to ship"
      reasoning: "Community content should help customers learn about the product itself, not individual ordering experiences"
      
    G6:
      title: "Pricing/Availability"
      description: "Review contains individual pricing experiences or store-specific availability comments not relevant to all customers"
      allowed_example: "Value comments about the product (e.g., 'For only $29, this blender is really great'), General availability wishes (e.g., 'I wish this book was also available in paperback')"
      not_allowed_example: "Individual pricing experiences (e.g., 'Found this item here for $5 less than at my local store'), Store-specific availability (e.g., 'My local Target doesn't carry this anymore')"
      reasoning: "These comments aren't relevant for all customers"
      
    G7:
      title: "Language Violation"
      description: "Content is written in languages other than English or Spanish or mixes languages inappropriately"
      allowed_example: "Content in the supported languages of the Amazon site"
      not_allowed_example: "Content in unsupported languages or mixed-language content"
      reasoning: "Content must be accessible to all users of the site"
      
    G8:
      title: "Spam/Repetitive Content"
      description: "Contains repetitive text, nonsense, gibberish, distracting punctuation/symbols, or ASCII art"
      allowed_example: "Coherent, non-repetitive text relevant to the product"
      not_allowed_example: "Repetitive text, nonsense/gibberish, content consisting mainly of punctuation/symbols, ASCII art"
      reasoning: "Such content is distracting and doesn't help customers make purchasing decisions"
      
    G9:
      title: "Private Information"
      description: "Shares personal information"
      allowed_example: "N/A"
      not_allowed_example: "Phone numbers, email addresses, mailing addresses, license plates, DSN, order numbers"
      reasoning: "Protects privacy and prevents identity theft"
      
    G10:
      title: "Profanity/Harassment"
      description: "Contains profanity, obscenities, name-calling, harassment, threats, attacks, libel, defamation, or inflammatory content"
      allowed_example: "Questioning beliefs or expertise respectfully, 'I hate the product', 'The product is crap'"
      not_allowed_example: "Profanity/obscenities/name-calling, harassment/threats, attacks on people, libel/defamation/inflammatory content, coordinated posting from multiple accounts. Using Only abuse"
      reasoning: "Maintains a respectful community environment"
      
    G11:
      title: "Hate Speech"
      description: "Expresses hatred based on protected characteristics or promotes organizations using hate speech"
      allowed_example: "N/A"
      not_allowed_example: "Hatred based on race, ethnicity, nationality, gender, gender identity, sexual orientation, religion, age, or disability, Promoting organizations that use such hate speech"
      reasoning: "Ensures an inclusive and respectful environment"
      
    G12:
      title: "Sexual Content"
      description: "Contains inappropriate sexual content"
      allowed_example: "Discussing sex and sensuality products sold on Amazon or products with sexual content"
      not_allowed_example: "Profanity/obscene language, content with nudity, sexually explicit images or descriptions"
      reasoning: "Maintains appropriate content standards"
      
    G13:
      title: "External Links"
      description: "Contains links to external sites, phishing, malware, or URLs with referrer/affiliate codes"
      allowed_example: "Links to other products on Amazon"
      not_allowed_example: "Links to external sites, phishing/malware sites, URLs with referrer tags or affiliate codes"
      reasoning: "Ensures user safety and prevents exploitation"
      
    G14:
      title: "Promotional Content"
      description: "Primary purpose is promoting a company, website, or special offer, or was created by someone with financial interest"
      allowed_example: "N/A"
      not_allowed_example: "Content whose MAIN purpose is promotion of another brand or product"
      reasoning: "Prevents conflicts of interest and ensures authentic reviews"
      
    G15:
      title: "Illegal Activities"
      description: "Encourages illegal activities"
      allowed_example: "N/A"
      not_allowed_example: "Content encouraging violence, illegal drug use, underage drinking, child/animal abuse, fraud, terrorism, Threats of physical/financial harm, fraudulent schemes, encouraging dangerous product misuse"
      reasoning: "Prevents promotion of harmful or illegal behavior"
      
    G16:
      title: "Medical Claims"
      description: "Makes statements about preventing or curing serious medical conditions"
      allowed_example: "N/A"
      not_allowed_example: "Claims related to preventing/curing serious medical conditions for any product type (including foods, beverages, supplements, cosmetics, personal care products)"
      reasoning: "Prevents potentially dangerous medical misinformation"

  scoring_system:
    0: "No violation"
    1: "Low confidence of violation (possibly non-compliant)"
    2: "Medium confidence of violation (likely non-compliant)" 
    3: "High confidence of violation (clearly non-compliant)"

  output_format:
    review_interpretation: "Primary interpretation of the review's content and intent, noting any ambiguities or alternative readings"
    ratings: "G1-G16 ratings from 0-3"
    top_violations: "Only if any violation rating >= 2, share the details, else List the three highest-rated violations in descending order by rating"