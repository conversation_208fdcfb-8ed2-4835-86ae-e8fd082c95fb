const prisma = require("../../database/prisma/getPrismaClient");
const getPpcData = require("../../services/ppc");
const { generateSlug } = require("../generateSlug");
const { generateAuditPdf } = require("../getAmazonAudit");
const Sentry = require("@sentry/node");

async function processPPCAudit(csvData, amazonData, clientId, auditPdf = false) {
  try {
    const ppcReport = await prisma.pPCData.findFirst({
      where: {
        slug: csvData.productSlug,
      },
    });

    if (ppcReport) {
      // Check if some fields are missing
      const isDataComplete =
        ppcReport.BrandedKeywordSearch &&
        Object.keys(ppcReport.BrandedKeywordSearch).length > 0 &&
        ppcReport.BrandedSearch &&
        Object.keys(ppcReport.BrandedSearch).length > 0 &&
        ppcReport.CompetitorSearch &&
        Object.keys(ppcReport.CompetitorSearch).length > 0 &&
        ppcReport.ProspectPDPImage &&
        Object.keys(ppcReport.ProspectPDPImage).length > 0;

      if (isDataComplete) {
        console.log("PPC Data Already Present...");
        if (auditPdf) {
          await generateAuditPdf(csvData);
        }
        return;
      }

      // Generate new PPC data to update missing fields
      console.log("Fetching new PPC data for updating missing fields...");
      const newPpcData = await getPpcData(csvData, clientId);

      // Update existing data with new data where applicable
      await prisma.pPCData.update({
        where: { id: ppcReport.id },
        data: {
          BrandedSearch: newPpcData.BrandedSearch || ppcReport.BrandedSearch,
          BrandedKeywordSearch:
            newPpcData.BrandedKeywordSearch || ppcReport.BrandedKeywordSearch,
          ProspectPDPImage:
            newPpcData.ProspectPDPImage || ppcReport.ProspectPDPImage,
          nonBrandedKeywordSearch:
            newPpcData.nonBrandedKeywordSearch ||
            ppcReport.nonBrandedKeywordSearch,
          CompetitorSearch:
            newPpcData.competitorBrandedData || ppcReport.CompetitorSearch,
        },
      });

      if (auditPdf) {
        await generateAuditPdf(csvData);
      }
      return;
    }

    if (amazonData.data) {
      // Handle case where `ppcReport` doesn't exist
      let result = null;
      if (csvData.prospectDetails) {
        const asin = csvData.prospectDetails.asin;
        console.log(`ProspectDetails ASIN: ${asin}`);
        result = amazonData.data.productData.find((item) => item.asin === asin);
        if (result) {
          console.log("Found ASIN in amazonData");
        } else {
          console.log("ASIN not found in amazonData");
        }
      }

      if (!result && amazonData.data.productData.length > 0) {
        result = amazonData.data.productData[0];
        console.log(
          `Using first ASIN from amazonData: ${JSON.stringify(result)}`
        );
      }

      console.log(
        "Generating PPC Audit Data for company:-----------------",
        csvData.companyName
      );

      const slug = generateSlug(
        amazonData.data.company_name +
          " " +
          csvData.prospectDetails.asin +
          " " +
          clientId
      );

      console.log("Getting PPC Audit....");
      const ppcData = await getPpcData(csvData, clientId);
      csvData["ppcAudit"] = ppcData;

      // Create new record
      await prisma.pPCData.create({
        data: {
          companyId: csvData.companyId,
          slug,
          BrandedSearch: ppcData.BrandedSearch,
          BrandedKeywordSearch: ppcData.BrandedKeywordSearch,
          ProspectPDPImage: ppcData.ProspectPDPImage,
          nonBrandedKeywordSearch: ppcData.nonBrandedKeywordSearch,
          CompetitorSearch: ppcData.competitorBrandedData,
        },
      });

      if (auditPdf) {
        await generateAuditPdf(csvData);
      }
    }
  } catch (error) {
    console.error("Error Stack:", error.stack);
    Sentry.captureException("Error in processPPCAudit:", error);
    console.error("Error in processing PPC Data:", error);
  }
}

module.exports = processPPCAudit;
