// /utils/bull/connection.js

require("dotenv").config();

const isProduction = process.env.NODE_ENV === "production";

const connection = {
  url: isProduction ? process.env.REDIS_URL_PROD : process.env.REDIS_URL_DEV,
};

console.log(
  `🚀 BullMQ initialized in ${isProduction ? "PRODUCTION" : "DEVELOPMENT"} mode`
);
// console.log(`🔗 Redis URL: ${connection.url}`);

if (!connection?.url) {
  throw new Error(
    "❌ Redis connection URL not defined. Check your environment variables!"
  );
}

if (!isProduction && process.env.REDIS_URL_DEV === process.env.REDIS_URL_PROD) {
  throw new Error(
    "❌ REDIS_URL_DEV and REDIS_URL_PROD cannot be the same! This is unsafe."
  );
}

module.exports = { connection, isProduction };
