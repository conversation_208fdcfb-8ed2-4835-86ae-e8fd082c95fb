const emailTemplates = {
  completed: {
    haveRevenue: `firstName, 

Was looking at your product on Amazon - the “prospectProductTitle”,

Going to be frank with you.

You’re textChoice1 over $revenueDifference Monthly in sales right now.

That is $revenueDifferenceYearly a year…

Look at competitorBrandName in your niche… Have you seen “competitorProductName”?

They are making ~ $competitorRevenue per month while you’re at $prospectRevenue.

Can I share a brief plan on how we’d beat them for companyName?

Best regards,
<<Your name>>

P.S. - <<Your offer comes here>>
`,
    noRevenue: `firstName,

We built the blueprint to rank #1 for “prospectProductTitle”. 

Your competitor competitorBrandName that is selling  “competitorProductName” is currently earning ~ $competitorRevenue per month. (same target market and product as you.)

In our blueprint we’ve outlined some optimisations that would make those look like BABY numbers.

Mind if we share our strategies?

Best regards,
<<Your name>>

P.S. - <<Your offer comes here>>`,
  },
  // Add more templates as needed
};

module.exports = emailTemplates;
