const emailTemplates = require("./emailTemplates");
const { getCompetitorEmailTemplate } = require("../../models/configuration");
// Function to process spin text
function processSpinText(str) {
  return str.replace(/{([^}]+)}/g, (match, options) => {
    const choices = options.split("|").map((choice) => choice.trim());
    const randomChoice = choices[Math.floor(Math.random() * choices.length)];
    return randomChoice;
  });
}

// Function to interpolate string with parameters and count placeholders
function interpolateStringOP(str, params) {
  let yetToBeAddedCount = 0;

  let interpolatedString = str.replace(/{{\s*([^}]+)\s*}}/g, (match, key) => {
    if (!params[key.trim()]) {
      yetToBeAddedCount++;
      return "<<Yet To be Added>>";
    }
    return params[key.trim()];
  });

  interpolatedString = processSpinText(interpolatedString);

  return { interpolatedString , yetToBeAddedCount};
}


async function generateCompEmail(finalData, clientId) {
  const emailTemplates = await getCompetitorEmailTemplate(clientId);
  // console.log({ finalData });
  // console.log({ emailTemplates });
  let emailContent = "";
  let subject = "Quick Question";
  let typeOfEmail = "completed";

  let compEmailStatus = "unsuccessful";

  if (
    finalData["Revenue difference monthly"] > "0" ||
    finalData["Revenue difference monthly"] > 0
  ) {
    if (
      finalData["Prospect revenue"] != "0" ||
      finalData["Prospect revenue"] > 0
    ) {
      emailContent = emailTemplates[typeOfEmail].haveRevenue;
      compEmailStatus = "success-with-revenue";
    } else {
      emailContent = emailTemplates[typeOfEmail].noRevenue;
      compEmailStatus = "success-without-revenue";
    }
  }

  // console.log({ emailContent , compEmailStatus });

  const { interpolatedString, yetToBeAddedCount } = interpolateStringOP(emailContent, finalData);
  if(yetToBeAddedCount > 0){
    compEmailStatus = "unsuccessful";
  }
  // console.log(  { interpolatedString, yetToBeAddedCount });
  return { subject, interpolatedString, compEmailStatus };
}

// generateCompEmail(finalData, 1).then(({ subject, emailContent }) => {
//   console.log("Subject:", subject);
//   console.log("Email Content:", emailContent);
// });

module.exports = { generateCompEmail };
