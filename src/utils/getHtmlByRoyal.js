const fs = require("fs");
const puppeteer = require("puppeteer-extra");
const StealthPlugin = require("puppeteer-extra-plugin-stealth");
const Sentry = require("@sentry/node");
const chromium = require("@sparticuz/chromium");

puppeteer.use(StealthPlugin());

const getProxyRoyalPass = require("./multicountry/getProxyPassRoyal");


const userAgents = [
  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
  "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Safari/605.1.15",
  // Add more User-Agent strings as needed
];
const randomUserAgent =
  userAgents[Math.floor(Math.random() * userAgents.length)];

async function getHtmlByRoyal(url, clientId , company_id) {
  console.log("Fetching data from url using ipRoyal:------------------", url);


chromium.setHeadlessMode = true;


  const validArgs = chromium.args.filter((arg) => typeof arg === "string");

  const browser = await puppeteer.launch({
    args: ["--proxy-server=geo.iproyal.com:12321", ...validArgs],
    defaultViewport: chromium.defaultViewport,
    executablePath: await chromium.executablePath(),
    headless: chromium.headless,
  });

  // const browser = await puppeteer.launch({
  //   headless: true,
  //   args: ["--proxy-server=geo.iproyal.com:12321"],
  // });
  const page = await browser.newPage();
  const password = await getProxyRoyalPass(company_id);
  console.log({password});
  await page.authenticate({
    username: "WZ65fQSVuCXhQZby",
    password: password,
  });

  // Set the user agent
  await page.setUserAgent(randomUserAgent);
  try {
    await page.goto(url, {
      timeout: 120000,
      waitUntil: "networkidle2", // Ensures the page is fully loaded
    });

    // Get the page content
    const data = await page.content();
    // fs.writeFileSync("product.html", data);

    console.log("Fetching URL Completed using ipRoyal---------------", { url });
    await browser.close();
    return data;
  } catch (error) {
    await browser.close();
    console.error("Error Stack:", error.stack);
    Sentry.captureException("Error in proxyCall", error);
  } finally {
    await browser.close();
  }
}

module.exports = getHtmlByRoyal;

// getHtmlByRoyal(
//   "https://www.amazon.com/MODA-Darkening-Age-Defying-Volumizing-Breakage/dp/B0CVLC7K6B/ref=sr_1_1_sspa?crid=QOVNDI8K2MCH&dib=eyJ2IjoiMSJ9.YQ4VbSx7HKCnvWSWnk5YtCAT-FZGNPnQd4Ph8Isf8-v0L1GvkWixgwJ-SuuQ4K348Q1CrWswPmNKTC4xoJliCkdZuV9AG1kcNLKKcOpo-yOzFKR0nqwy3n2oHj5DmhBpfRmIfYjkZX3R_O3YnPLviP55Td77SMsCx2Yce9ORTwQc0KQ0sPCrqMlwOShh6GD-bldO4Lzj11JhA5a-LK3mlauEw55GE9s85oiedhlT8vvm7crme6JEeO-fLyu1I21UnN7oa9Mb3vvTZmEt6QmD9Y7vVkOn2pzfZ9ywUrVLiDw.seODVfqAbRJrVSEQS6gtKJ3xXLTR3Nl6DYCFa9in9T4&dib_tag=se&keywords=moda&qid=1728069984&sprefix=mo%2Caps%2C566&sr=8-1-spons&sp_csd=d2lkZ2V0TmFtZT1zcF9hdGY&psc=1"
// );
