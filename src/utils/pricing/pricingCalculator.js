const Sentry = require("@sentry/node");

let pricing = {
  scraperApi: {   
    creditUsed: 0,
    pages: [],
  },
  scrapingBee: {
    creditUsed: 0,
    pages: [],
  },
  jungleScout: {
    creditUsed: 0,
    products: [],
  },
  chatGPT: {
    totalCost: 0,
    inputToken: 0,
    outputToken: 0,
    totalToken: 0,
    details: [],
  },
};

function scraperApiPricingCalculator(url) {
  try {
    pricing.scraperApi.creditUsed +=5;
    pricing.scraperApi.pages.push(url);
  } catch (error) {
    Sentry.captureException(
      new Error(`Error calculating scraperAPI pricing: ${error}`)
    );
    throw new Error(`Error calculating scraperAPI pricing: ${error}}`);
  }
}

function scrapingBeePricingCalculator(url) {
  try {
    pricing.scrapingBee.creditUsed +=25;
    pricing.scrapingBee.pages.push(url);
  } catch (error) {
    Sentry.captureException(
      new Error(`Error calculating scrapingBee pricing: ${error}`)
    );
    throw new Error(`Error calculating scrapingBee pricing: ${error}}`);
  }
}

function jungleScoutPricingCalculator(asin) {
  try {
    pricing.jungleScout.creditUsed +=1;
    pricing.jungleScout.products.push(asin);
  } catch (error) {
    Sentry.captureException(
      new Error(`Error calculating jungleScout pricing: ${error}`)
    );
    throw new Error(`Error calculating jungleScout pricing: ${error}}`);
  }
}

function chatGPTPricingCalculator(result, type) {
  try {
    pricing.chatGPT.inputToken += result.prompt_tokens;
    pricing.chatGPT.outputToken += result.completion_tokens;
    pricing.chatGPT.totalToken += result.total_tokens;
    pricing.chatGPT.details.push({
      type,
      prompt_tokens: result.prompt_tokens,
      completion_tokens: result.completion_tokens,
      total_tokens: result.total_tokens,
    });
  } catch (error) {
    Sentry.captureException(
      new Error(`Error calculating chatGPT pricing: ${error}`)
    );
    throw new Error(`Error calculating chatGPT pricing: ${error}}`);
  }
}

function getTotalPrice() {
  return pricing;
}

function resetPricing() {
  console.log({ pricing });
  pricing.scraperApi.pages = [];
  pricing.scraperApi.creditUsed = 0;
  pricing.scrapingBee.pages = [];
  pricing.scrapingBee.creditUsed = 0;
  pricing.jungleScout.creditUsed = 0;
  pricing.jungleScout.products = [];
  pricing.chatGPT.details = [];
  pricing.chatGPT.inputToken = 0;
  pricing.chatGPT.outputToken = 0;
  pricing.chatGPT.totalToken = 0;

  console.log("PRICING RESET");
  console.log("==============================================");
}

module.exports = {
  getTotalPrice,
  resetPricing,
  scraperApiPricingCalculator,
  scrapingBeePricingCalculator,
  chatGPTPricingCalculator,
  jungleScoutPricingCalculator,
};
