/**
 * Creates a standardized key from an Amazon URL by removing protocol and keeping specific query parameters
 * @param {string} url - The Amazon URL to process
 * @param {Object} paramConfig - Configuration for which query params to keep for different paths
 * @returns {string} - The processed URL string for use as a key
 */
function createKeyFromAmazonUrl(
  url,
  paramConfig = { s: ["seller"], s: ["me"] }
) {
  try {
    // Remove http:// or https:// from the URL
    let processedUrl = url.replace(/^https?:\/\//, "");

    // Parse the URL to extract components
    const urlObj = new URL(
      url.startsWith("http") ? url : `https://${processedUrl}`
    );

    // Get the pathname without leading slash
    const path = urlObj.pathname.replace(/^\//, "");

    // Get the first part of the path (before any additional slashes)
    const pathType = path.split("/")[0];

    // Check if we have configuration for this path type
    if (paramConfig[pathType]) {
      // Create a new URLSearchParams object
      const params = new URLSearchParams();

      // Get the current search params
      const currentParams = new URLSearchParams(urlObj.search);

      // Only keep the specified parameters for this path type
      for (const paramName of paramConfig[pathType]) {
        if (currentParams.has(paramName)) {
          params.set(paramName, currentParams.get(paramName));
        }
      }

      // Reconstruct the URL with only the domain, path type, and specified query params
      processedUrl = `${urlObj.hostname.replace(/^www\./, "")}/${pathType}${
        params.toString() ? `?${params.toString()}` : ""
      }`;
    } else {
      // If no specific configuration for this path type, return the URL as is
      processedUrl = url.replace(/^https?:\/\//, "").replace(/^www\./, "");
    }
    console.log(processedUrl);
    return processedUrl;
  } catch (error) {
    console.error("Error processing Amazon URL:", error);
    // If there's an error, return a sanitized version of the original URL
    return url.replace(/^https?:\/\//, "").replace(/^www\./, "");
  }
}

module.exports = createKeyFromAmazonUrl

// createKeyFromAmazonUrl("https://www.amazon.co.uk/s?me=A1SFIMCH4IZ3GA&hgfdashgaf");