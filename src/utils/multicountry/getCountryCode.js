const prisma = require("../../database/prisma/getPrismaClient");

const countryMap = {
  com: "us",
  "co.uk": "gb",
  de: "de",
  ca: "ca",
  fr: "fr",
  in: "in",
};
async function getStoreCountryCode(company_id, amazonURL) {
  // Extract the domain from the storefront URL
  let url = amazonURL;
  if (!url) {
    const data = await prisma.company.findFirst({
      where: {
        id: company_id,
      },
    });
    if (!data) {
      return "us";
    }
    url = data.storeFrontURL || data.productUrl || data.searchUrl;
  }

  // Use a regex that matches the domain suffix (e.g., .com, .co.uk)
  const domainMatch = url.match(/amazon\.([a-z\.]+)(?:\/|$)/);
  // console.log({ domainMatch });
  if (!domainMatch) return "us"; // Default to 'us' if no match is found

  const domainSuffix = domainMatch[1]; // Extract just the suffix (e.g., 'co.uk')
  const country_code = countryMap[domainSuffix] || "us";
  return country_code;
}
module.exports = getStoreCountryCode;
