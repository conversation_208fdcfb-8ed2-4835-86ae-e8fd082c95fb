const yaml = require("js-yaml");
const fs = require("fs");
const path = require("path");

const DEFAULT_YML_PATH = path.join(__dirname, "..", "config", "yml", "default.yml");

class ContextManager {
  constructor(initialUtils = {}) {
    this.store = {
      ...(this.store || {}),
      ...initialUtils,
    };
  }

  addUtils(newUtils) {
    this.store = {
      ...this.store,
      ...newUtils,
    };
  }

  getContext() {
    return this.store;
  }
  addToStore(key, value) {
    this.store[key] = value;
  }
}

function evaluateCondition(condition, contextManager) {
  // Handle pure store conditions
  // console.log(contextManager.getContext());
  if (condition.store && !condition.type) {
    Object.entries(condition.store).forEach(([key, expression]) => {
      const context = contextManager.getContext();
      const params = [...Object.keys(context)];
      const values = [...Object.values(context)];
      // console.log(context?.shortPoints);
      // console.log(context);
      contextManager.addToStore(
        key,
        new Function(...params, `return ${expression}`)(...values)
      );
    });
    return false;
  }

  // Process store if exists alongside type
  if (condition.store) {
    Object.entries(condition.store).forEach(([key, expression]) => {
      const context = contextManager.getContext();
      const params = [...Object.keys(context)];
      const values = [...Object.values(context)];
      // console.log(context);
      contextManager.addToStore(
        key,
        new Function(...params, `return ${expression}`)(...values)
      );
    });
  }
  const context = contextManager.getContext();
  return new Function(...Object.keys(context), `return ${condition.when}`)(
    ...Object.values(context)
  );
}

function parseYamlReport(reportCategory, store, filePath = DEFAULT_YML_PATH) {
  // const yamlPath = path.join(__dirname, '..', 'config', 'yml', 'default2.yml');
  const yamlConfig = parseYamlConfig(filePath);
  const contextManager = new ContextManager(store);
  const report = [];

  // const orderedReportCategories = Object.keys(yamlConfig).sort((a, b) => {
  //   return (yamlConfig[a].auditRank || Infinity) - (yamlConfig[b].auditRank || Infinity);
  // });


  yamlConfig?.[reportCategory]?.conditions?.forEach((condition) => {
    if (evaluateCondition(condition, contextManager)) {
      const reportConfig = yamlConfig?.[reportCategory]?.[condition.type];

      if (!reportConfig) {
        console.log(`No report config found for ${condition.type}`);
        return;
      }

      report.push(
        generateReport(
          reportConfig,
          contextManager.getContext(),
          yamlConfig[reportCategory].dataPoint || reportCategory
        )
      );
    }
  });


  return report;
}

function orderedYamlParser(store, fileName = "default.yml") {
  try {
    const yamlConfig = parseYamlConfig(fileName);
    const orderedReportCategories = Object.keys(yamlConfig).sort((a, b) => {
      return (yamlConfig[a].auditRank || Infinity) - (yamlConfig[b].auditRank || Infinity);
    });

    const report = [];
    orderedReportCategories.forEach((reportCategory) => {
      report.push(...parseYamlReport(reportCategory, store, fileName));
    });

    return report;
  } catch (e) {
    console.log(e);
  }

}
function parseYamlConfig(filePath) {
  const yamlPath = filePath || DEFAULT_YML_PATH;
  return yaml.load(fs.readFileSync(yamlPath, "utf8"));
}

function generateReport(reportConfig, data, dataPoint) {
  const report = {
    DATA_POINT: dataPoint,
    PRIORITY: reportConfig.priority || "Medium",
    Logic: reportConfig.logic,
    PAIN_POINT: reportConfig.painPoint,
    Improvements: reportConfig.improvements,
    Benefits: reportConfig.benefits,
  };

  // Replace placeholders in all fields
  for (const key in report) {
    if (typeof report[key] === "string") {
      report[key] = replacePlaceholders(report[key], data);
    } else if (Array.isArray(report[key])) {
      report[key] = report[key].map((item) => replacePlaceholders(item, data)).filter(el => el);
    }
  }

  return report;
}

function replacePlaceholders(text, data) {
  // Handle ternary expressions
  if (!text) {
    return null
  }
  
  text = text.replace(/{{(.*?)}}/g, (match, expression) => {
    if (expression.includes('?')) {
      const params = Object.keys(data);
      const values = Object.values(data);
      return new Function(...params, `return ${expression}`)(...values);
    }
    // Handle simple variable replacement
    return data[expression] !== undefined ? data[expression] : match;
  });
  return text;
}

function getFilePathFromRegionAndClientId(clientId, region) {
  const defaultClientPath = path.join(__dirname, "..", "config", "yml", String(clientId), "default.yml");
  if (fs.existsSync(defaultClientPath)) {
    return defaultClientPath;
  }
  return DEFAULT_YML_PATH;
}

module.exports = { parseYamlReport, orderedYamlParser, getFilePathFromRegionAndClientId };
