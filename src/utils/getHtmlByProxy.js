const axios = require("axios");
const fs = require("fs");
const { sendErrorEmail } = require("./mailHelper");
const Sentry = require("@sentry/node");
const { getScraperKeys } = require("../models/configuration");
const getProxyUsername = require("./multicountry/getProxyUsername");
const { scraperApiPricingCalculator } = require("./pricing/pricingCalculator");
const { S3Service } = require("../services/aws/s3/uploadHTMLTos3");
const createKeyFromAmazonUrl = require("../utils/s3Utils/createKeyFromAmazonUrl");

require("dotenv").config();

async function getHtmlByProxy(url, clientId, retries, company_id) {
  console.log("Fetching data from url:------------------", url);
  const {
    SCRAPER_API_MAX_RETRIES,
    SCRAPER_API_HOST,
    SCRAPER_API_PORT,
    SCRAPER_API_USERNAME,
    SCRAPER_API_KEY,
  } = await getScraperKeys(clientId);

  // Set default retries if not provided
  if (retries === undefined) {
    retries = SCRAPER_API_MAX_RETRIES;
  }
  const scraperApiUsername = company_id
    ? await getProxyUsername(company_id)
    : SCRAPER_API_USERNAME;
  try {
    const response = await axios.get(url, {
      proxy: {
        host: SCRAPER_API_HOST,
        port: SCRAPER_API_PORT,
        auth: {
          username: scraperApiUsername,
          password: SCRAPER_API_KEY,
        },
        protocol: "http",
      },
    });

    console.log("Fetching URL Completed---------------", { url });
    const data = response.data;
    // fs.writeFileSync("product.html", data);
    scraperApiPricingCalculator(url);
    return data;
  } catch (error) {
    console.error("Error Stack:", error.stack);
    Sentry.captureException("Error in proxyCall", error);
    if (error.response && error.response.status === 403) {
      // sendErrorEmail("proxyError")
      console.log("[Error in proxyCall]", error.response.data);
      throw new Error("403 You have used up all your API credits.");
    }
    if (error.response && error.response.status === 404) {
      console.log("PAGE NOT FOUND!");
      return {};
    }

    if (retries > 0) {
      console.log(`[getHtmlByProxy]: Retrying... (${retries} retries left)`);
      return await getHtmlByProxy(url, clientId, retries - 1);
    } else {
      console.log("[Error in proxyCall]", error);
      throw new Error("Max retries exceeded. Unable to fetch data.");
    }
  }
}

async function getHtmlFromS3(url, clientId, company_id) {
  const S3_CACHE_TTL_HOURS = parseInt(process.env.S3_CACHE_TTL_HOURS || "24");
  const cleanUrl = createKeyFromAmazonUrl(url);
  const cacheKey = cleanUrl.replace(/[^a-zA-Z0-9]/g, "_");

  try {
    const hasValidCache = await S3Service.objectIsValid(
      cacheKey,
      S3_CACHE_TTL_HOURS
    );

    if (hasValidCache) {
      console.log("Found valid cached HTML for URL in S3:", cleanUrl);
      const result = await S3Service.getFromS3(cacheKey, false, true);

      if (result && result.data) {
        console.log(
          "Using cached HTML from S3 (cached at:",
          result.metadata["scraped-timestamp"] || "unknown time",
          ")"
        );
        return result.data;
      }
    }

    // Either no cache or expired cache
    console.log("Fetching fresh HTML for URL:", cleanUrl);
    const data = await getHtmlByProxy(
      url,
      clientId,
      undefined,
      company_id
    );

    if (data) {
      const metadata = {
        "original-url": cleanUrl,
        "scraped-timestamp": new Date().toISOString(),
      };
      await S3Service.uploadToS3(data, cacheKey, false, metadata);
      console.log("Stored fresh HTML in S3 cache for URL:", cleanUrl);
      return data;
    }

    return null;
  } catch (error) {
    console.error("Error Stack:", error.stack);

    if (error.response && error.response.status === 404) {
      console.log("PAGE NOT FOUND!");
      try {
        const metadata = {
          "original-url": cleanUrl,
          "scraped-timestamp": new Date().toISOString(),
          status: "404",
        };
        await S3Service.uploadToS3(
          "<!-- 404 PAGE NOT FOUND -->",
          cacheKey,
          false,
          metadata
        );
        console.log("Stored 404 marker in S3 cache for URL:", url);
      } catch (cacheError) {
        console.error("Error storing 404 marker in S3 cache:", cacheError);
      }
      return {};
    }

    throw error;
  }
}

module.exports = { getHtmlByProxy, getHtmlFromS3 };

// getHtmlFromS3(
//   "https://www.amazon.co.uk/s?me=A1UU3WTU5J9FY0&marketplaceID=A1F83G8C2ARO7P"
// );
// getHtmlByProxy("https://www.amazon.co.uk/s?me=A1SFIMCH4IZ3GA");
