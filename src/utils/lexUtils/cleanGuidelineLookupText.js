function cleanGuidelineLookupText(text) {
  if (!text || typeof text !== 'string') {
    return '';
  }

  let cleanedText = text.trim();
  
  // Remove the "Title:" line completely (everything from start until first newline after "Title:")
  cleanedText = cleanedText.replace(/^Title:.*?\n/i, '');
  
  // Remove the "Description:" header but keep the content after it
  cleanedText = cleanedText.replace(/^Description:\s*/i, '');
  
  // Clean up any extra whitespace
  cleanedText = cleanedText.trim();
  
  return cleanedText;
}

module.exports = { cleanGuidelineLookupText };