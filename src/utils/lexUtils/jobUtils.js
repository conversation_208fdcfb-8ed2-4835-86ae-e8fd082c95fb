/**
 * Generates unique job names following modern naming conventions
 */
function generateJobName(type, identifier) {
  const timestamp = new Date()
    .toISOString()
    .replace(/[-:]/g, "")
    .replace("T", "_")
    .split(".")[0];

  const randomSuffix = Math.random().toString(36).substring(2, 8).toUpperCase();

  return `Job_${type}_${identifier}_${timestamp}_${randomSuffix}`;
}

function generateSellerJobName(sellerId) {
  return generateJobName("sellerID", sellerId);
}

function generateAsinJobName(asin) {
  return generateJobName("asin", asin);
}

function generateReviewJobName(asin) {
  return generateJobName("review", asin);
}

function generateBulkReviewJobName() {
  const timestamp = new Date().toISOString().split("T")[0].replace(/-/g, "");
  const randomSuffix = Math.random().toString(36).substring(2, 8).toUpperCase();
  return `Job_review_bulk_${timestamp}_${randomSuffix}`;
}

module.exports = {
  generateSellerJobName,
  generateAsinJobName,
  generateReviewJobName,
  generateBulkReviewJobName,
};
