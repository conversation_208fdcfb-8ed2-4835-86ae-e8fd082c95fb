//Run this script to count LexASIN records with null sellerId
const prisma = require("../../database/prisma/getPrismaClient");

async function countNullSellerIdAsins() {
  try {
    console.log("🔍 Counting LexASIN records with null sellerId...");
    
    const count = await prisma.lexASIN.count({
      where: {
        sellerId: null
      }
    });
    
    console.log(`📊 Total LexASIN records with null sellerId: ${count}`);
    
    // Optional: Get some additional stats for context
    const totalAsins = await prisma.lexASIN.count();
    const withSellerIdCount = await prisma.lexASIN.count({
      where: {
        sellerId: {
          not: null
        }
      }
    });
    
    console.log(`📈 Statistics:`);
    console.log(`   - Total ASINs: ${totalAsins}`);
    console.log(`   - ASINs with sellerId: ${withSellerIdCount}`);
    console.log(`   - ASINs with null sellerId: ${count}`);
    console.log(`   - Percentage with null sellerId: ${((count / totalAsins) * 100).toFixed(2)}%`);
    
    return count;
    
  } catch (error) {
    console.error("❌ Error counting null sellerId ASINs:", error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
// countNullSellerIdAsins()
//   .then((count) => {
//     console.log(`✅ Script completed successfully. Found ${count} records with null sellerId.`);
//     process.exit(0);
//   })
//   .catch((error) => {
//     console.error("💥 Script failed:", error);
//     process.exit(1);
//   });