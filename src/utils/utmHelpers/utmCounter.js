const prisma = require("../../database/prisma/getPrismaClient");

const utmCounter = async ({
  n_uuid,
  uuid,
  clientName,
  sellerId,
  type,
  source,
  campaign,
}) => {
  try {
    setImmediate(async () => {
      // Define search criteria to include the source
      const searchCriteria = { clientName, sellerId, type, source, campaign };

      // console.log({ searchCriteria });

      // Check if the log already exists for the given criteria
      const existingLog = await prisma.uTMLogs.findFirst({
        where: searchCriteria,
      });

      // console.log({ existingLog });

      if (existingLog) {
        // Increment the count for the existing log with the specific source
        await prisma.uTMLogs.update({
          where: { id: existingLog.id },
          data: { count: { increment: 1 } },
        });
        console.log(
          `Log updated for UUID: ${
            uuid ? uuid : n_uuid
          }, client: ${clientName}, and source: ${source}. Count incremented.`
        );
      } else {
        // Fetch source data from UTMSync using the same criteria (excluding source)
        let sourceData = null;
        if (n_uuid) {
          sourceData = await prisma.uTMSyncNew.findFirst({
            where: {
              uuid: n_uuid,
            },
          });
        } else {
          sourceData = await prisma.uTMSync.findFirst({
            where: {
              clientName,
              sellerId,
              type,
            },
          });
        }

        console.log({ sourceData });

        if (sourceData) {
          // Create a new log entry for the specific source
          await prisma.uTMLogs.create({
            data: {
              uuid: sourceData.uuid,
              url: sourceData.url,
              clientName: sourceData.clientName,
              sellerId: sourceData.sellerId,
              type: sourceData.type,
              campaign,
              source,
              count: 1,
            },
          });

          console.log(
            `New log created for UUID: ${
              uuid ? uuid : n_uuid
            }, client: ${clientName}, and source: ${source}.`
          );
        } else {
          console.log(
            `No matching data found in UTMSync for the given criteria (excluding source):`,
            { clientName, sellerId, type }
          );
        }
      }
    });
  } catch (error) {
    console.error("Error during async logging:", error);
  }
};

module.exports = utmCounter;
