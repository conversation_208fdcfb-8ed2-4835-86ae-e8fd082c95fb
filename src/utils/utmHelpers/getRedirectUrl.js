const prisma = require("../../database/prisma/getPrismaClient");

const getRedirectUrl = async (uuid, clientName, sellerId, type) => {
  try {

    if (
      (type === "testimonials" ||
        type === "ctaLink" ||
        type === "caseStudies") &&
      (!uuid || !clientName)
    ) {
      throw new Error(
        "For testimonials, ctaLink, or caseStudies, both uuid and clientName are required."
      );
    }

    if (
      !(
        type === "testimonials" ||
        type === "ctaLink" ||
        type === "caseStudies"
      ) &&
      (!uuid || !clientName || !sellerId || !type)
    ) {
      throw new Error(
        "For other types, uuid, clientName, sellerId, and type are all required."
      );
    }

    const url = await prisma.uTMSync.findFirst({
      where:
        type === "testimonials" || type === "ctaLink" || type === "caseStudies"
          ? { uuid, clientName }
          : { clientName, sellerId, type },
    });

    if (!url) {
      console.log("No URL found with these UTM params:", {
        uuid,
        clientName,
        sellerId,
        type,
      });
      return "";
    }
    return url;
  } catch (error) {
    console.log("Error while redirecting:", error);
    return ""; // Return an empty string if there's an error or no match found
  }
};

module.exports = getRedirectUrl;
