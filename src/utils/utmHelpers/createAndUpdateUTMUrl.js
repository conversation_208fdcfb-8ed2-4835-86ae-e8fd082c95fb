const prisma = require("../../database/prisma/getPrismaClient");
const getRedirectUrl = require("./getRedirectUrl");
const { v5: uuidv5 } = require("uuid");
const getRedirectUrlwithUUId = require("./getRedirectUrlwithUUID");

const createAndUpdateUTMUrl = async ({
  url,
  clientName,
  sellerId,
  email,
  type,
  campaign,
}) => {
  try {
    const baseUrl = "https://www.amazongrowthaudit.com/redirect";
    if (!url) {
      throw new Error(
        "ERROR during creating/updating utm params : URL cannot be empty."
      );
    }

    const uuidInput = `${url} + ${clientName} + ${sellerId} + ${type} + ${email} +${campaign}`;
    const namespace = "6ba7b810-9dad-11d1-80b4-00c04fd430c8";
    const uuid = uuidv5(uuidInput, namespace);

    // console.log("UUID:", { uuid, uuidInput });

    // Fetch existing UTM data based on the condition
    const utmData = await getRedirectUrlwithUUId(uuid);

    // If UTM data exists, return the existing UTM URL
    if (utmData) {
      console.log("UTM data already exists:", utmData.uuid);
      await prisma.uTMSyncNew.update({
        where: {
          id: utmData.id,
        },
        data: {
          url,
          email,
          campaign,
        },
      });
      console.log("Updated UTM Data For:", uuid);
      return generateUtmUrl(baseUrl, { uuid });
    }

    console.log("Creating utm url with:", { url, uuid });

    // Generate and return the UTM URL
    const redirectUrl = generateUtmUrl(baseUrl, { uuid });

    console.log("Generated UTM URL:", redirectUrl);

    // Create a new UTM entry in the database
    await prisma.uTMSyncNew.create({
      data: {
        url,
        uuid,
        redirectUrl,
        clientName,
        sellerId,
        type,
        campaign,
        email
      },
    });

    return redirectUrl;
  } catch (error) {
    console.error("Error Creating UTM URL:", error.message);
    return "";
  }
};

function generateUtmUrl(baseUrl, options = {}) {
  const { uuid } = options;
  const params = new URLSearchParams();

  if (uuid) params.append("n_uuid", uuid);

  const utmUrl = `${baseUrl}${
    baseUrl.includes("?") ? "&" : "?"
  }${params.toString()}`;
  return utmUrl;
}

module.exports = createAndUpdateUTMUrl;

// Example usage:
// createAndUpdateUTMUrl({
//   url: "https://www.example.com",
//   clientName: "mukul",
//   sellerId: "fd",
//   type: "caseStudies",
//   campaign: "test-campaign",
//   email: "<EMAIL>",
// })
//   .then(console.log)
//   .catch(console.error);
