const puppeteer = require("puppeteer-extra");
const StealthPlugin = require("puppeteer-extra-plugin-stealth");
const { getScrapingBeeKeys } = require("../../models/configuration");
const getStoreCountryCode = require("../multicountry/getCountryCode");
const { getBrowser } = require("./browserHelper");
const { scrapingBeePricingCalculator } = require("../pricing/pricingCalculator");
require("dotenv").config();

puppeteer.use(StealthPlugin());

const userAgents = [
  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
  "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Safari/605.1.15",
  // Add more User-Agent strings as needed
];
const randomUserAgent =
  userAgents[Math.floor(Math.random() * userAgents.length)];

async function connectPuppeteer(url, company_id, clientId) {
  const { SCRAPING_BEE_API_KEY } = await getScrapingBeeKeys(clientId);
  const browser = await getBrowser();

  const page = await browser.newPage();
  await page.setUserAgent(randomUserAgent);
  await page.setViewport({
    width: 1920,
    height: 1080,
  });
  try {
    const country_code = await getStoreCountryCode(company_id, url);
    // const country_code = "us";
    const scrapingBeeUrl = `https://app.scrapingbee.com/api/v1?api_key=${SCRAPING_BEE_API_KEY}&url=${encodeURIComponent(
      url
    )}&premium_proxy=true&country_code=${country_code}`;
    console.log(scrapingBeeUrl);

    await page.goto(scrapingBeeUrl, {
      timeout: 0,
      waitUntil: "networkidle2", // Ensures the page is fully loaded
    });
     scrapingBeePricingCalculator(url);
    const acceptBtnSelector = "#sp-cc-accept";
    if (await page.$(acceptBtnSelector)) {
      console.log("Cookie Accept Button Pressed.");
      await page.click(acceptBtnSelector);
    } else {
      console.log("No Cookie Accept Button.");
    }
  } catch (e) {
    console.error(e);
  }

  return { browser, page };
}

module.exports = connectPuppeteer;

// connectPuppeteer(
//   "https://www.amazon.com/s?k=nike+shoes",
// );
