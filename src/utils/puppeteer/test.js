const auditImageScreenshot = require("../scrapeAmazonUtils/screenshotHelper/generateMailAuditScreenShot");

async function testSingletonBrowser() {
  const testUrls = [
    {
      url: "https://www.equalcollective.com/jeff/audit/mavis-tire-supply-llc-b00pufq85g-4",
      slug: "test1",
    },
    {
      url: "https://www.equalcollective.com/jeff/audit/mavis-tire-supply-llc-b00pufq85g-4",
      slug: "test2",
    },
  ];

  console.log("Starting singleton browser test...");
  for (const { url, slug } of testUrls) {
    console.log(`Testing with slug: ${slug}`);
    const result = await auditImageScreenshot(url, slug);
    if (result) {
      console.log(`Successfully processed screenshot for slug: ${slug}`);
    } else {
      console.error(`Failed to process screenshot for slug: ${slug}`);
    }
  }

  console.log("Test completed.");
}

testSing<PERSON><PERSON>rowser()
  .then(() => console.log("All tests finished."))
  .catch((err) => console.error("Error during test:", err));
