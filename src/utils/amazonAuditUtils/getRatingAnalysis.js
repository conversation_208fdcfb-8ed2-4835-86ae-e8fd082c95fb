const { getRatingCategory } = require("../scrapeAmazonUtils/getRating");

function getRatingGoal(rating) {
  if (!rating) return 0;
  if (rating < 3.5) return rating + 0.5;
  if (rating < 3.7) return 3.8;
  if (rating < 4.3) return 4.3;
  if (rating < 4.7) return 4.8;
  return 5;
}

function getStarRating(star, reviews) {
  return reviews.reviewPerStar.find((review) => star in review)?.[star] || 0;
}

function calculateRatingAfterAdding5Stars(
  totalRatings,
  totalWeightedScore,
  goalRating
) {
  if (!totalRatings) return "N/A";

  let added5Stars = 0;

  while (true) {
    const newTotalRatings = totalRatings + added5Stars;
    const newWeightedScore = totalWeightedScore + added5Stars * 5;
    const newAverageRating = newWeightedScore / newTotalRatings;

    // Prevent infinite loop with a max iteration threshold
    if (added5Stars > 10000) return "N/A";

    if (newAverageRating >= goalRating) return added5Stars;
    added5Stars++;
  }
}

function calculateRatingAfterRemovingLowStars(
  totalRatings,
  totalWeightedScore,
  oneStarCount,
  twoStarCount,
  goalRating
) {
  if (!totalRatings) return "N/A";

  let removed1Stars = 0;
  let removed2Stars = 0;

  while (oneStarCount > 0 || twoStarCount > 0) {
    if (oneStarCount > 0) {
      totalRatings--;
      totalWeightedScore -= 1;
      oneStarCount--;
      removed1Stars++;
    } else if (twoStarCount > 0) {
      totalRatings--;
      totalWeightedScore -= 2;
      twoStarCount--;
      removed2Stars++;
    }

    const newAverageRating = totalWeightedScore / totalRatings;
    if (newAverageRating >= goalRating) {
      return { removed1Stars, removed2Stars };
    }

    // Safeguard in case no more stars to remove
    if (oneStarCount === 0 && twoStarCount === 0) return "N/A";
  }

  return "N/A"; // Goal not achievable
}

function getRatingAnalysis(csvData) {
  try {
    console.log("Getting Rating Analysis...");
    const prospectASIN = csvData.prospectDetails?.asin;
    if (!prospectASIN) {
      throw new Error("NO ASIN found in prospect data");
    }
    const productData = csvData.amazonAudit.productData.find(
      (product) => product.asin === prospectASIN
    );

    if (!productData) {
      throw new Error("Prospect product not found in productData.");
    }

    const { rating, review } = productData;

    const stars = [
      "1starReview",
      "2starReview",
      "3starReview",
      "4starReview",
      "5starReview",
    ];
    const [
      oneStarReview,
      twoStarReview,
      threeStarReview,
      fourStarReview,
      fiveStarReview,
    ] = stars.map((star) => getStarRating(star, review));

    const totalNoOfRatings = review?.totalReviewCountInt || 0;
    const noOfStars = rating?.rating || 0;
    const noOfStarsComesAs = rating?.ratingComesAs || 0;

    const goalForNumberOfStars = getRatingGoal(noOfStarsComesAs);
    const goalForNumberOfStarsComesAs = getRatingCategory(goalForNumberOfStars);

    const totalWeightedScore =
      oneStarReview * 1 +
      twoStarReview * 2 +
      threeStarReview * 3 +
      fourStarReview * 4 +
      fiveStarReview * 5;

    const numbOf5StarNeeded = calculateRatingAfterAdding5Stars(
      totalNoOfRatings,
      totalWeightedScore,
      goalForNumberOfStars
    );

    const lowStarRemovalResult = calculateRatingAfterRemovingLowStars(
      totalNoOfRatings,
      totalWeightedScore,
      oneStarReview,
      twoStarReview,
      goalForNumberOfStars
    );

    console.log("Rating Analysis Done...");
    return {
      oneStarReview,
      twoStarReview,
      totalNoOfRatings,
      noOfStars,
      noOfStarsComesAs,
      goalForNumberOfStars,
      goalForNumberOfStarsComesAs,
      numbOf5StarNeeded,
      lowStarRemovalResult,
    };
  } catch (error) {
    console.error("Error in getRatingAnalysis:", error.message);
    return null;
  }
}

module.exports = getRatingAnalysis;

const csvData = {
  companyId: 22,
  companyName: "aavrani", //sellerName
  brandName: "aavrani",
  prospectDetails: {
    asin: "B013498LQI",
    revenue: 1173,
    productName:
      "Aloe-Cran Sugar Free Drink Mix Supplement| Aloe Vera and Whole Cranberry Powder with Dietary Fiber| Supports Digestion and Energy Levels|Great Sugar-Free Taste - 30 Servings",
    revenueSource: "The 30-day sales number on Amazon",
    productAmazonURL: "https://www.amazon.com/dp/B013498LQI",
    humanizedProspectName: "Naturecity",
    humanizedProspectProductTitle: "sugar free drink mix supplement",
  },
  amazonAudit: {
    store: {
      store_front_url:
        "/stores/NatureCity/page/FB3B19DD-B2C5-496D-B4A7-BFAE57CF19A9?ref_=ast_bln&store_ref=bl_ast_dp_brandLogo_sto",
      storefront_present: true,
    },
    productData: [
      {
        url: "https://www.amazon.com/dp/B013498LQI?language=en_GB",
        asin: "B013498LQI",
        price: 23,
        sales: 51,
        title:
          "Aloe-Cran Sugar Free Drink Mix Supplement| Aloe Vera and Whole Cranberry Powder with Dietary Fiber| Supports Digestion and Energy Levels|Great Sugar-Free Taste - 30 Servings",
        images: { noOfImages: 6, noOfVideos: 3, noOfImagesRaw: 9 },
        rating: { rating: 4.4, ratingComesAs: 4.5 },
        review: {
          reviews: "N/A",
          reviewMissing: false,
          reviewPerStar: [
            { "5starReview": 106 },
            { "4starReview": 12 },
            { "3starReview": 21 },
            { "2starReview": 4 },
            { "1starReview": 6 },
          ],
          reviewsCategory: "Helpful",
          totalReviewCountInt: 147,
        },
        brandName: "naturecity",
        outOfStock: false,
        description:
          "DELICIOUS & NUTRITIOUS – Makes a delicious sugar-free drink with organic Aloe vera, whole cranberry fruit powder and 5 grams of dietary fiber per serving. No artificial sweeteners, or preservatives. Non-GMO and KETO friendly, great taste and only 15 calories per serving.     DIGESTION & INTESTINAL SUPPORT – Helps balance stomach acid levels, promote healthy digestion and nutrient absorption, relieve occasional heartburn and gastrointestinal discomfort. Also helps you maintain already-healthy cholesterol levels.     URINARY TRACT HEALTH – The PACran whole cranberry powder in this drink mix is scientifically studied to support urinary tract and prostate health. The urinary tract health benefits of PACran come without the sugar found in cranberry juice. Provides antioxidant support too.     5 GRAMS OF FIBER WITHOUT GAS OR BLOATING – Non-GMO Fibersol-2 is a soluble prebiotic fiber that ferments much more slowly in the large intestine than other fibers - so it’s unlikely to cause unwanted effects like gas and bloating. Over 100 scientific studies indicate it helps you stay regular and increase the volume of waste excreted.     TRUSTED USA BASED SUPPLMENT BRAND FOR OVER 20 YEARS – For 22 years, NatureCity has been making premium quality nutritional supplements that put science and nature to work for you. Every NatureCity product is manufactured in the USA by cGMP certified manufacturers using the best of nature from around the globe. No cheap substitutes are used in any products.",
        priceString: "23.",
        AplusContent: false,
        bulletPoints: {
          value:
            " DELICIOUS & NUTRITIOUS – Makes a delicious sugar-free drink with organic Aloe vera, whole cranberry fruit powder and 5 grams of dietary fiber per serving. No artificial sweeteners, or preservatives. Non-GMO and KETO friendly, great taste and only 15 calories per serving.   DIGESTION & INTESTINAL SUPPORT – Helps balance stomach acid levels, promote healthy digestion and nutrient absorption, relieve occasional heartburn and gastrointestinal discomfort. Also helps you maintain already-healthy cholesterol levels.   URINARY TRACT HEALTH – The PACran whole cranberry powder in this drink mix is scientifically studied to support urinary tract and prostate health. The urinary tract health benefits of PACran come without the sugar found in cranberry juice. Provides antioxidant support too.   5 GRAMS OF FIBER WITHOUT GAS OR BLOATING – Non-GMO Fibersol-2 is a soluble prebiotic fiber that ferments much more slowly in the large intestine than other fibers - so it’s unlikely to cause unwanted effects like gas and bloating. Over 100 scientific studies indicate it helps you stay regular and increase the volume of waste excreted.   TRUSTED USA BASED SUPPLMENT BRAND FOR OVER 20 YEARS – For 22 years, NatureCity has been making premium quality nutritional supplements that put science and nature to work for you. Every NatureCity product is manufactured in the USA by cGMP certified manufacturers using the best of nature from around the globe. No cheap substitutes are used in any products.  ",
          Points: [
            {
              value:
                "DELICIOUS & NUTRITIOUS – Makes a delicious sugar-free drink with organic Aloe vera, whole cranberry fruit powder and 5 grams of dietary fiber per serving. No artificial sweeteners, or preservatives. Non-GMO and KETO friendly, great taste and only 15 calories per serving.",
              NumberChars: 271,
              FirstWordCapital: true,
            },
            {
              value:
                "DIGESTION & INTESTINAL SUPPORT – Helps balance stomach acid levels, promote healthy digestion and nutrient absorption, relieve occasional heartburn and gastrointestinal discomfort. Also helps you maintain already-healthy cholesterol levels.",
              NumberChars: 240,
              FirstWordCapital: true,
            },
            {
              value:
                "URINARY TRACT HEALTH – The PACran whole cranberry powder in this drink mix is scientifically studied to support urinary tract and prostate health. The urinary tract health benefits of PACran come without the sugar found in cranberry juice. Provides antioxidant support too.",
              NumberChars: 273,
              FirstWordCapital: true,
            },
            {
              value:
                "5 GRAMS OF FIBER WITHOUT GAS OR BLOATING – Non-GMO Fibersol-2 is a soluble prebiotic fiber that ferments much more slowly in the large intestine than other fibers - so it’s unlikely to cause unwanted effects like gas and bloating. Over 100 scientific studies indicate it helps you stay regular and increase the volume of waste excreted.",
              NumberChars: 336,
              FirstWordCapital: true,
            },
            {
              value:
                "TRUSTED USA BASED SUPPLMENT BRAND FOR OVER 20 YEARS – For 22 years, NatureCity has been making premium quality nutritional supplements that put science and nature to work for you. Every NatureCity product is manufactured in the USA by cGMP certified manufacturers using the best of nature from around the globe. No cheap substitutes are used in any products.",
              NumberChars: 358,
              FirstWordCapital: true,
            },
          ],
          TotalChars: 1478,
          isItAllCaps: false,
        },
        company_name: "naturecity",
        productTitle: {
          value:
            "Aloe-Cran Sugar Free Drink Mix Supplement| Aloe Vera and Whole Cranberry Powder with Dietary Fiber| Supports Digestion and Energy Levels|Great Sugar-Free Taste - 30 Servings",
          numOfChars: 173,
          titleUnder150Chars: false,
          titleApprox200Chars: false,
        },
        categoryAndRank: ["N/A"],
        textDescription: { value: "", numOfChars: 0 },
      },
    ],
    company_name: "naturecity",
  },
};
// console.log(getRatingAnalysis(csvData));
