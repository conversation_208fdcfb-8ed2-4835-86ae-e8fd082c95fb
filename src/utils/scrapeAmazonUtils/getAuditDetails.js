const cheerio = require("cheerio");
const fs = require("fs");

const { selectors } = require("../../services/scrapeAmazon/selectors");
const extractRating = require("./getRating");
const getReviews = require("./getReviews");
const getCategoryAndRank = require("./getCategoryAndRank");
const getStoreFront = require("./getStoreFront");
const { MAX_NO_OF_REVIEWS } = require("../../services/scrapeAmazon/constant");
const { getHtmlByProxy } = require("../../utils/getHtmlByProxy");

function getAuditDetails(htmlData, company_name) {
  const $ = cheerio.load(htmlData);

  const titleElement = $(selectors.nameElement);
  if (titleElement.length === 0) {
    return null;
  }

  const title = $(selectors.nameElement).first().text().trim();
  const titleNumOfChars = title.length;
  const titleApprox200Chars = titleNumOfChars >= 180 && titleNumOfChars <= 220;
  const titleUnder150Chars = titleNumOfChars <= 150;
  const productTitle = {
    value: title,
    numOfChars: titleNumOfChars,
    titleApprox200Chars: titleApprox200Chars,
    titleUnder150Chars: titleUnder150Chars,
  };

  const bulletPoints = getBulletPoints($);

  const textDescriptionElement = $("#productDescription").text().trim();
  const textDescriptionChars = textDescriptionElement.length;
  const textDescription = {
    value: textDescriptionElement,
    numOfChars: textDescriptionChars,
  };

  const noOfImagesRaw = $(
    '#altImages [data-action]:not([data-action=""])'
  ).length;
  let noOfVideos = 0;
  // console.log("VIDEOS:", $(".video-count").text().trim().length);
  // console.log("VIDEO TEXT:", $(".video-count").text().trim());
  noOfVideos = parseInt($(".video-count").text().split(" ")[0] || 0);
  // console.log("NUM OF VIDEOS:",noOfVideos)
  if (!noOfVideos) {
    if ($(".video-count").text().trim()) {
      noOfVideos = 1;
    } else {
      noOfVideos = 0;
    }
  }
  const noOfImages = noOfImagesRaw - 1 - (noOfVideos ? 1 : 0);
  const images = {
    noOfImages,
    noOfVideos,
    noOfImagesRaw,
  };

  // const ads = isSponsoredCheck({ htmlData, keyword: company_name });

  const rating = extractRating($, selectors.ratingElement);
  // const numOfRating = extractRating($, selectors.numOfRating);
  const priceString = $(selectors.priceElement).first().text().trim();
  const price = parseFloat(priceString.replace(/\$/g, "")) || 0;
  const salesString = $(selectors.lastMonthSale).text().trim() || "N/A";
  let sales = 0;
  if (salesString !== "N/A") {
    const numberMatch = salesString.match(/(\d+(\.\d+)?)([K]?)\+/);
    if (numberMatch) {
      let number = parseFloat(numberMatch[1]);
      const unit = numberMatch[3];
      if (unit === "K") {
        number *= 1000;
      }
      // Add a random number between 1 and 9
      sales = Math.floor(number) + Math.floor(Math.random() * 9) + 1;
    }
  }
  const categoryAndRank = getCategoryAndRank($);
  const review = getReviews($, MAX_NO_OF_REVIEWS);
  const store = getStoreFront($);

  // const numOfAplusImages =
  // const numOfAplusVideos =

  const aplusContentPresent = $("#aplus").length > 0;

  // Initialize an array to store alt text presence
  // let aPlusAltTextPresent = [];
  let allImagesHaveAltText = true;

  if (aplusContentPresent) {
    // Get all img tags within the aplus section
    $("#aplus img").each((index, element) => {
      const altText = $(element).attr("alt");
      if (!altText) {
        allImagesHaveAltText = false;
      }
    });
  }
  if (!aplusContentPresent) {
    allImagesHaveAltText = false;
  }
  const AplusContent = {
    aplusContentPresent,
    allImagesHaveAltText,
  };

  return {
    company_name,
    productTitle,
    bulletPoints,
    textDescription,
    AplusContent,
    price: price,
    rating: rating,
    sales: sales,
    images,
    categoryAndRank,
    store: store,
    review,
  };
}
module.exports = { getAuditDetails };

async function getProductData() {
  const clientId = "1";
  const url =
    "https://www.amazon.com/OFF-Spray-Plant-Babies-Toddlers/dp/B09GDB6K8Q/ref=sr_1_8?dib=eyJ2IjoiMSJ9.Xt1ElzntOfsrzchGYbvOW8-zUMHOvRm3IjW4THQHx5IbT6Pjwy-jmMoM5TqVznM8BdxlUUaCqOeltxeSuW4cRE6yVNf042lzDN-2jHalq_oMD6UoOQciLe3klAjUTWO_puSP0PUKvQrSp3TOHZMUglO0gAvhLSqzm7K8Tkx5p1QjX7Q67jF__5nIrU2kg1rFSLpJmI2nWWDdmBga15yy-SPdbfsbe4rSielmXT6A3x44PJfl2NUOA3RSK09ENEAMwp2dcn-A-7u9D4Xb1s7x8viYtsrKeYVGGUnFnZGlnLk.yKo1KmC-aM6pWxDj3SyOnfm9jwKglVAkUQV6vvLuiiw&dib_tag=se&keywords=sunscreen%2Binsect%2Brepellent%2Bspray&qid=1720609672&s=apparel&sr=1-8";
  const htmlData = await getHtmlByProxy(url, clientId);
  const company_name = "MTN OPS";
  const data = getAuditDetails(htmlData, company_name);
  fs.writeFileSync("output.json", JSON.stringify(data));
  console.log("------done-----");
  console.log({ data });
}
// getProductData();
