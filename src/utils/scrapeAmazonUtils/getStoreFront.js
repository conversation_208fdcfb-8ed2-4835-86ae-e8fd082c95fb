const { selectors } = require("../../services/scrapeAmazon/selectors");

function storeFront($) {
  try {
    const storeFrontElement = $(selectors.storeLink);

    if (storeFrontElement.length === 0) {
      return {
        storefront_present: false,
        store_front_url: "N/A",
      };
    }
    const storeFrontUrl = storeFrontElement.attr("href");
    const isStoreFrontPresent =
      storeFrontUrl.includes("store") && storeFrontUrl.includes("page");
    return {
      storefront_present: isStoreFrontPresent,
      store_front_url: storeFrontUrl,
    };
  } catch (error) {
    console.error("Error Stack:", error.stack);
    return {
      storefront_present: false,
      store_front_url: "N/A",
    };
  }
}

module.exports = storeFront;
