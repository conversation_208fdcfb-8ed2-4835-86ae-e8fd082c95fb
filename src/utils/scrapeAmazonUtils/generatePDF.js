const puppeteer = require("puppeteer-extra");
const StealthPlugin = require("puppeteer-extra-plugin-stealth");
const chromium = require("@sparticuz/chromium");
const { uploadPDF } = require("../../services/aws/s3/index");
const { getBrowser, closePages } = require("../puppeteer/browserHelper");


puppeteer.use(StealthPlugin());

async function generatePDF(url, slug) {
  // Launch Puppeteer
  const browser = await getBrowser();
  try {
    const page = await browser.newPage();
    await page.goto(url, { timeout: 60000, waitUntil: "networkidle2" });
    await page.setViewport({ width: 1280, height: 2000 });

    // Wait for all images to load
    await page.evaluate(async () => {
      const images = Array.from(document.images);
      await Promise.all(
        images.map((img) => {
          if (img.complete) return Promise.resolve();
          return new Promise((resolve, reject) => {
            img.onload = resolve;
            img.onerror = reject;
          });
        })
      );
    });

    const containerHeight = await page.evaluate(() => {
      const container = document.querySelector("#outer-container");
      if (container) {
        return container.offsetHeight;
      }
      return null;
    });
    console.log("Calculated page height:", containerHeight);
    
    const pdfBuffer = await page.pdf({
      width: 1280,
      height: (containerHeight + 1000) * 0.75,
      printBackground: true,
      preferCSSPageSize: true,
      scale: 0.75,
      landscape: false,
    });

    await uploadPDF(pdfBuffer, `${slug}.pdf`);
    console.log("PDF generated and uploaded successfully");
    return true;
  } catch (error) {
    console.error("Error Stack:", error.stack);
    console.error(
      `Error occurred while generating PDF for company - ${slug}: `,
      error.message
    );
  } finally {
    await closePages(browser);
  }
}

module.exports = generatePDF;

// const url =
//   "https://www.equalcollective.com/jeff/audit/yesyoucan-b07lb4f5bb-17";
// const slug = "yesyoucan-b07lb4f5bb-17";
// generatePDF(url, slug);
