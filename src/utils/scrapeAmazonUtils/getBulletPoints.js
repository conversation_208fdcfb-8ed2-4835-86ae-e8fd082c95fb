const cheerio = require("cheerio");

function isFirstWordAllCaps(str) {
  const firstWord = str.split(" ")[0];
  return firstWord === firstWord.toUpperCase();
}

function getBulletPoints($) {
  let bulletPoints = {
    value: "",
    TotalChars: 0,
    isItAllCaps: false,
    Points: [],
  };

  let isAllCaps = (str) => str === str.toUpperCase();
  const pattern1 = $(".a-list-item.a-size-base.a-color-base").text(); // All bullet points have class = "a-list-item a-size-base a-color-base"
  const pattern2 = $("#feature-bullets").text(); // All bullet points are under a div with id = "feature-bullets"

  const len1 = pattern1.length;
  const len2 = pattern2.length;

  if (pattern1) {
    // Select all span tags with the specified class and build the structure
    const bulletPointsContainer = $(
      "span.a-list-item.a-size-base.a-color-base"
    );
    bulletPoints.value = bulletPointsContainer.text().trim();
    bulletPointsContainer.each((_, element) => {
      const text = $(element).text().trim();
      const numberChars = text.length;
      const FirstWordAllCaps = isFirstWordAllCaps(text);

      bulletPoints.Points.push({
        value: text,
        NumberChars: numberChars,
        FirstWordCapital: FirstWordAllCaps,
      });

      bulletPoints.TotalChars += numberChars;
    });

    bulletPoints.isItAllCaps = bulletPoints.Points.every((point) =>
      isAllCaps(point.value)
    );
  }

  if (pattern2) {
    // Select all span tags within the div with id "feature-bullets" and build the structure
    const bulletPointsContainer = $("#feature-bullets .a-list-item");
    bulletPoints.value = bulletPointsContainer.text();
    bulletPointsContainer.each((_, element) => {
      const text = $(element).text().trim();
      const numberChars = text.length;
      const FirstWordAllCaps = isFirstWordAllCaps(text);
      bulletPoints.Points.push({
        value: text,
        NumberChars: numberChars,
        FirstWordCapital: FirstWordAllCaps,
      });

      bulletPoints.TotalChars += numberChars;
    });

    bulletPoints.isItAllCaps = bulletPoints.Points.every((point) =>
      isAllCaps(point.value)
    );
  }

  // Output the result
  // console.log(bulletPoints, bulletPoints.Points);
  return bulletPoints;
}

module.exports = getBulletPoints;
