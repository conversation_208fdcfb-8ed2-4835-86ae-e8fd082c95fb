const puppeteer = require("puppeteer-extra");
const StealthPlugin = require("puppeteer-extra-plugin-stealth");
const axios = require("axios");
const chromium = require("@sparticuz/chromium");
const { uploadImage, getS3Url } = require("../../../services/aws/s3/index");
const { getBrowser, closePages } = require("../../puppeteer/browserHelper");
const getStoreCountryCode = require("../../multicountry/getCountryCode");
const { getScraperKeys } = require("../../../models/configuration")
const { scraperApiPricingCalculator } = require("../../pricing/pricingCalculator");

async function getAmazonScreenshots(
  clientId,
  url,
  slug,
  company_id,
  maxRetries = 3
) {
  puppeteer.use(StealthPlugin());
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    const browser = await getBrowser();
    const { SCRAPER_API_KEY } = await getScraperKeys(clientId);
    try {
      console.log("Capturing product for:", slug);
      chromium.setHeadlessMode = true;
      chromium.setGraphicsMode = false;

      await chromium.font(
        "https://raw.githack.com/googlei18n/noto-emoji/master/fonts/NotoColorEmoji.ttf"
      );

      const page = await browser.newPage();
      // const proxyUsername = await getProxyUsername(company_id);
      let country_code = await getStoreCountryCode(company_id, url);
      if (country_code == "gb") {
        country_code = "uk";
      }
      // Use ScraperAPI directly with Puppeteer
      const scraperApiUrl = `http://api.scraperapi.com?api_key=${SCRAPER_API_KEY}&url=${encodeURIComponent(
        url
      )}&country_code=${country_code}`;
      // console.log("SCRAPER API URL FOR AMAZON SCREENSHOT:", scraperApiUrl);

      try {
        console.log("Attempting to navigate to:", scraperApiUrl);
        await page.goto(scraperApiUrl, {
          waitUntil: "domcontentloaded",
          timeout: 60000,
        });
        console.log("Navigation successful.");
        scraperApiPricingCalculator(scraperApiUrl);
      } catch (error) {
        console.error("Navigation error:", error.message);
      }

      const acceptBtnSelector = "#sp-cc-accept";
      if (await page.$(acceptBtnSelector)) {
        console.log("Cookie Accept Button Pressed.");
        await page.click(acceptBtnSelector);
      } else {
        console.log("No Cookie Accept Button.");
      }

      await page.setViewport({
        width: 1920,
        height: 1080,
        deviceScaleFactor: 2,
      });

      // Scroll to the top of the page
      await page.evaluate(() => {
        window.scrollTo(0, 0);
      });

      const productImage = await page.$(".imageBlockRearch");
      if (productImage) {
        const box = await productImage.boundingBox();
        const productImageBuffer = await page.screenshot({
          encoding: "binary",
          clip: {
            x: box.x,
            y: box.y,
            width: box.width + 20,
            height: box.height + 20,
          },
        });
        await uploadImage(productImageBuffer, `${slug}_product_image.png`);
      } else {
        console.log(`Attempt ${attempt}: Product image not found.`);
        if (attempt < maxRetries) {
          console.log("Retrying...");
          continue; // Retry if product image is missing
        } else {
          console.error("Max retries reached. Product image not found.");
        }
      }

      const wholePageBuffer = await page.screenshot({ encoding: "binary" });
      await uploadImage(wholePageBuffer, `${slug}_page_image.png`);

      console.log("Screenshots captured and uploaded successfully");

      return;
    } catch (error) {
      console.error("Error Stack:", error.stack);
      console.error("Error occurred while capturing product:", error.message);
      if (attempt === maxRetries) {
        console.error("All retries failed.");
        throw error; // Rethrow error after max retries
      }
    } finally {
      await closePages(browser);
    }
  }
}

module.exports = getAmazonScreenshots;

// (async () => {
//   const url =
//     "https://www.amazon.com/product-reviews/B00BPUY3W0/ref=cm_cr_arp_d_viewopt_sr?ie=UTF8&reviewerType=all_reviews&filterByStar=one_star&pageNumber=1F";
//   const screenshots = await getAmazonScreenshots(
//     1,
//     url,
//     "proxy-browser-test"
//   );
//   // console.log(screenshots);
// })();
