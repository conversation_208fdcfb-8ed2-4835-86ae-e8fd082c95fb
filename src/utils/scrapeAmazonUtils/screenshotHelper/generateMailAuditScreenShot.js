const puppeteer = require("puppeteer-extra");
const StealthPlugin = require("puppeteer-extra-plugin-stealth");
const { uploadImage } = require("../../../services/aws/s3/index");
const { getBrowser, closePages } = require("../../puppeteer/browserHelper");

puppeteer.use(StealthPlugin());

async function auditImageScreenshot(url, slug) {
  console.log("Generating Audit Screenshot for company:", slug);

  const browser = await getBrowser();

  try {
    const page = await browser.newPage();
    await page.goto(url, { timeout: 5000, waitUntil: "networkidle2" });

    await page.setViewport({ width: 1280, height: 2000 });
    const elementSelector = "tbody.MuiTableBody-root tr:nth-child(2)";
    await page.waitForSelector(elementSelector);
    const element = await page.$(elementSelector);
    const boundingBox = await element.boundingBox();
    const headerSelector = "#link-title";
    await page.waitForSelector(headerSelector);
    const header = await page.$(headerSelector);
    const headerBox = await header.boundingBox();

    // Take a full-page screenshot first
    await page.screenshot({
      fullPage: true, // Capture the entire page
      encoding: "binary", // You can change encoding to 'base64' if needed
    });
    const clip = {
      x: 0, // Start from the left edge of the page
      y: headerBox.y + headerBox.height, // Start below the header
      width: page.viewport().width, // Full width of the viewport
      height:
        boundingBox.y + boundingBox.height - (headerBox.y + headerBox.height), // Height from below the header to the target element
    };

    // Take a screenshot of the element
    const auditImageBuffer = await page.screenshot({
      encoding: "binary",
      //   path: "partial-screenshot.png",
      clip: clip, // Crop to the area from the top to the element
    });
    await uploadImage(auditImageBuffer, `${slug}_audit_image.png`);

    console.log("Audit Screenshot generated and uploaded successfully");
    return true;
  } catch (error) {
    console.error("Error Stack:", error.stack);
    console.error(
      `Error occurred while generating PDF for company - ${slug}: `,
      error.message
    );
  } finally {
    await closePages(browser);
  }
}

module.exports = auditImageScreenshot;

// const url =
//   "https://www.equalcollective.com/jeff/audit/mavis-tire-supply-llc-b00pufq85g-4";
// const slug = "adhikram";
// auditImageScreenshot(url, slug);
