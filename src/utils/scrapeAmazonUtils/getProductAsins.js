const cheerio = require("cheerio");

const { selectors } = require("../../services/scrapeAmazon/selectors");
const Sentry = require("@sentry/node");

function getProductAsins({ htmlData, keyword, numOfProducts }) {
  try {
    const $ = cheerio.load(htmlData);
    keyword = keyword.toLowerCase();
    const products = [];

    $(selectors.productContainer).each((index, container) => {
      const title = $(container)
        .find(selectors.productTitle)
        .text()
        .toLowerCase();
      // console.log(title)
      const titleCheck = title.includes(keyword);
      const header = $(container)
        .find(selectors.brandHeader)
        .text()
        .toLowerCase();
      // console.log(titleCheck)
        // const headerList = header?header.split(" "):false;
        // const headerCheck = header
        //   ? headerList.some((keyword) => header.includes(keyword))
        //   : false;
      const headerCheck = header.includes(keyword);

      // console.log({ title, header, keyword, titleCheck, headerCheck });

      if (headerCheck || titleCheck) {
        const sponsorCheck = $(container).find(selectors.sponsoredIcon);
        if (sponsorCheck.length === 0) {
          const asin = $(container).attr("data-asin");
          const ratings =
            parseInt(
              $(container)
                .find(selectors.numOfRatings)
                .text()
                .trim()
                .replace(",", "")
            ) || 0;
          // console.log(ratings);

          const monthlySalesText = $(container)
            .find(selectors.sales)
            .first()
            .text();
          // console.log(monthlySalesText)

          const monthlySales = monthlySalesText.includes("100+")
            ? 100
            : parseInt(monthlySalesText) || 0;
          // console.log(monthlySales)

          const priceWhole = $(container)
            .find(selectors.priceWhole)
            .text()
            .trim();
          const priceFraction = $(container)
            .find(selectors.priceFraction)
            .text()
            .trim();
          const price = parseFloat(priceWhole + priceFraction) || 0;
          products.push({ asin, ratings, monthlySales, price });
        }
      }
    });

    // Sort the products by monthly sales in descending order,
    // then by revenue (price * monthlySales) in descending order,
    // then by ratings in descending order
    const sortedProducts = products.sort((a, b) => {
      if (b.monthlySales !== a.monthlySales) {
        return b.monthlySales - a.monthlySales;
      } else if (b.price * b.monthlySales !== a.price * a.monthlySales) {
        return b.price * b.monthlySales - a.price * a.monthlySales;
      } else {
        return b.ratings - a.ratings;
      }
    });

    return sortedProducts
      .slice(0, numOfProducts)
      .map((product) => product.asin);
  } catch (error) {
    console.error("Error Stack:", error.stack);
    Sentry.captureException("Error Getting Product Asins:", error);
    console.log("Error Getting Product Asins For:", keyword);
    return [];
  }
}

module.exports = getProductAsins;
