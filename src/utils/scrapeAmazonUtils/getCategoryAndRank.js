const cheerio = require("cheerio");
const { selectors } = require("../../services/scrapeAmazon/selectors");
const Sentry = require("@sentry/node");
const { getHtmlByProxy } = require("../getHtmlByProxy");

function cleanCategory(category) {
  return category.replace(/[^\w\s&]/g, "").trim();
}

function getCategoryAndRank($) {
  try {
    const ranksAndCategories = [];

    // Log the full HTML to see what you're working with
    // console.log($.html()); // Logs the whole parsed HTML

    const tableRows = $(selectors.bsrRowElement);

    // Log tableRows to see if the selector worked
    // console.log("tableRows:", tableRows.html());

    if (tableRows.length > 0) {
      const rankTh = tableRows.find(selectors.bsrHeaderElement);

      // Log rankTh to check the selection
      // console.log("rankTh:", rankTh.html());

      if (rankTh.length > 0) {
        const rankTd = rankTh.next("td");

        // Log rankTd to verify its content
        // console.log("rankTd:", rankTd.html());

        const ranks = [];

        // Loop through all span elements inside rankTd to extract rank and category
        rankTd.find("span span").each(function () {
          const spanText = $(this).text(); // Use $(this), not cheerio(this)

          // Log each spanText to see what it's capturing
          // console.log("spanText:", spanText);

          // Extract the rank using regex
          const rankMatch = spanText.match(/(\d+)/);

          if (rankMatch) {
            const rank = rankMatch[1];

            // Extract the category by removing the rank and "in" from the text
            const categoryText = spanText
              .replace(/^\d+ in\s*/, "")
              .split("(")[0]
              .trim();

            // Log rank and category extracted
            // console.log({ rank, category: categoryText });
  
            ranks.push({ rank, category: categoryText });
          }
        });

        // console.log({ ranks });

        const rankText = rankTd.text().trim();
        // console.log({ rankText });
        const cleanedRankText = rankText.replace(/\s*\(.*?\)\s*/g, " ").trim();
        // console.log({ cleanedRankText });
const regex = /([\d,]+)\s*in\s*([^\d]+)/g;
        let matches;

       while ((matches = regex.exec(cleanedRankText)) !== null) {
         const rankNumber = parseInt(matches[1].replace(/,/g, ""), 10); // Convert rank to a number
         const cleanCat = cleanCategory(matches[2].trim()); // Clean category
        //  console.log({ rankNumber, cleanCat });

         // Push the rank and category to the array
         ranksAndCategories.push({
           rank: rankNumber,
           category: cleanCat,
         });
       }

        // Sort ranks in ascending order
        ranksAndCategories.sort((a, b) => a.rank - b.rank);
        return ranksAndCategories.length > 0 ? ranksAndCategories : "N/A";
      }
    }

    // Fallback: try extracting ranks from a list of elements
    const rankListItem = $(selectors.bsrListElement);
    // console.log("RankListItem:", rankListItem.html()); // Log the fallback element
    const mainRankMatch = rankListItem.text().match(/#(\d+,?\d*)/);
    // console.log("MainRankMatch:", mainRankMatch);
    const mainRank = mainRankMatch
      ? parseInt(mainRankMatch[0].replace(/#/g, "").replace(/,/g, ""), 10)
      : NaN;

    const mainCategoryMatch = rankListItem
      .text()
      .match(/in\s*(.+?)\s*(\(|#|$)/);
    const mainCategory = mainCategoryMatch
      ? cleanCategory(mainCategoryMatch[1].trim())
      : "N/A";

    if (!isNaN(mainRank)) {
      ranksAndCategories.push({ rank: mainRank, category: mainCategory });
    }

    const subRanksList = rankListItem.find(selectors.bsrRankElement);
    subRanksList.find("li").each((i, el) => {
      const rankText = $(el).text().trim();
      // console.log({rankText})
      const rankMatch = rankText.match(/\d+,?\d*/);
      const rank = rankMatch
        ? parseInt(rankMatch[0].replace(/#/g, "").replace(/,/g, ""), 10)
        : NaN;

      const categoryMatch = rankText.match(/in\s*(.+?)\s*(\(|#|$)/);
      const category = categoryMatch
        ? cleanCategory(categoryMatch[1].trim())
        : "N/A";

      if (!isNaN(rank)) {
        ranksAndCategories.push({ rank, category });
      }
    });

    ranksAndCategories.sort((a, b) => a.rank - b.rank);
    return ranksAndCategories.length > 0 ? ranksAndCategories : ["N/A"];
  } catch (error) {
    console.error("Error Stack:", error.stack);
    Sentry.captureException("Error in Getting Category and Rank:", error);
    return "N/A";
  }
}

module.exports = getCategoryAndRank;

async function Example() {
  try {
    const url =
      "https://amazon.co.uk/dp/B08WQBVWGC";
    // const url = "https://www.amazon.com/dp/B0068A02LI";

    const htmlData = await getHtmlByProxy(url, 1);
    const $ = cheerio.load(htmlData);
    const data = getCategoryAndRank($);
    console.log({ data });
  } catch (error) {
    console.log({ error });
  }
}

// Example();
