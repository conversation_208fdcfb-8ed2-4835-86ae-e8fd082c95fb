-- CreateE<PERSON>
CREATE TYPE "ReviewJobStatus" AS ENUM ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED');

-- CreateEnum
CREATE TYPE "ReviewCheckerStatus" AS ENUM ('PENDING', 'PRESENT', 'REMOVED', 'FAILED', 'RESURRECTED');

-- AlterTable
ALTER TABLE "LexReview" ADD COLUMN     "checkerStatus" "ReviewCheckerStatus" NOT NULL DEFAULT 'PENDING',
ADD COLUMN     "comments" TEXT NOT NULL DEFAULT '',
ADD COLUMN     "lexReviewCheckerJobId" INTEGER,
ADD COLUMN     "next_run" TIMESTAMP(3),
ADD COLUMN     "removedAt" TIMESTAMP(3),
ADD COLUMN     "removedHistory" JSONB NOT NULL DEFAULT '[]',
ADD COLUMN     "returnedHistory" JSONB NOT NULL DEFAULT '[]',
ADD COLUMN     "run_frequency" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "totalRuns" INTEGER NOT NULL DEFAULT 0;

-- CreateTable
CREATE TABLE "LexReviewCheckerJob" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "status" "ReviewJobStatus" NOT NULL DEFAULT 'PENDING',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "LexReviewCheckerJob_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "LexReviewCheckerOutputData" (
    "id" SERIAL NOT NULL,
    "revId" INTEGER NOT NULL,
    "reviewJobId" INTEGER NOT NULL,
    "status" "ReviewCheckerStatus" NOT NULL DEFAULT 'PENDING',
    "reviewUrl" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "LexReviewCheckerOutputData_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "LexReview" ADD CONSTRAINT "LexReview_lexReviewCheckerJobId_fkey" FOREIGN KEY ("lexReviewCheckerJobId") REFERENCES "LexReviewCheckerJob"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LexReviewCheckerOutputData" ADD CONSTRAINT "LexReviewCheckerOutputData_revId_fkey" FOREIGN KEY ("revId") REFERENCES "LexReview"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LexReviewCheckerOutputData" ADD CONSTRAINT "LexReviewCheckerOutputData_reviewJobId_fkey" FOREIGN KEY ("reviewJobId") REFERENCES "LexReviewCheckerJob"("id") ON DELETE CASCADE ON UPDATE CASCADE;
