-- Create<PERSON><PERSON>
CREATE TYPE "JobStatus" AS ENUM ('PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED');

-- CreateEnum
CREATE TYPE "LexJobType" AS ENUM ('SELLER_ID', 'SING<PERSON>_ASIN', '<PERSON><PERSON><PERSON><PERSON>_ASIN', 'SINGLE_REVIEW', 'BULK_REVIEW');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "CookieStatus" AS ENUM ('ACTIVE', 'EXPIRE', 'UNKNOWN');

-- CreateEnum
CREATE TYPE "AsinStatus" AS ENUM ('PENDING', 'SCRAPED', 'FAILED');

-- CreateTable
CREATE TABLE "User" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "userType" TEXT NOT NULL,
    "slug" TEXT NOT NULL DEFAULT '',
    "signature" TEXT NOT NULL DEFAULT '',
    "logo" TEXT NOT NULL DEFAULT '',
    "ctaLink" TEXT NOT NULL DEFAULT '',
    "bgColor" TEXT NOT NULL DEFAULT '',
    "caseStudies" JSONB NOT NULL DEFAULT '{}',
    "testimonials" JSONB NOT NULL DEFAULT '[]',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "mbLogo" TEXT NOT NULL DEFAULT '',
    "tableLogo" TEXT NOT NULL DEFAULT '',
    "googleSheetURL" TEXT NOT NULL DEFAULT '',
    "offerLine" TEXT NOT NULL DEFAULT '',
    "website" TEXT NOT NULL DEFAULT '',
    "caseStudiesText" TEXT NOT NULL DEFAULT 'Read full case study here',
    "ctaButtonText" TEXT NOT NULL DEFAULT '',
    "calendlyId" TEXT NOT NULL DEFAULT '',

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Configurations" (
    "id" SERIAL NOT NULL,
    "clientId" INTEGER NOT NULL,
    "scraperApi" JSONB NOT NULL DEFAULT '{}',
    "jungleScout" JSONB NOT NULL DEFAULT '{}',
    "openAi" JSONB NOT NULL DEFAULT '{}',
    "caseStudies" JSONB NOT NULL DEFAULT '{}',
    "compEmailTemplates" JSONB NOT NULL DEFAULT '{}',
    "auditEmailPrompts" JSONB NOT NULL DEFAULT '{}',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "promptTemplates" JSONB NOT NULL DEFAULT '{}',
    "singleEntryOpt" BOOLEAN NOT NULL DEFAULT false,
    "scrapingBeeApi" JSONB NOT NULL DEFAULT '{}',

    CONSTRAINT "Configurations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Company" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "clientId" INTEGER NOT NULL,
    "website" TEXT NOT NULL,
    "jobId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "productUrl" TEXT NOT NULL DEFAULT '',
    "searchUrl" TEXT NOT NULL DEFAULT '',
    "storeFrontURL" TEXT NOT NULL DEFAULT '',

    CONSTRAINT "Company_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Job" (
    "id" SERIAL NOT NULL,
    "clientId" INTEGER NOT NULL,
    "status" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "singleCompany" BOOLEAN NOT NULL DEFAULT false,
    "ppcAudit" BOOLEAN NOT NULL DEFAULT false,
    "campaignId" INTEGER,
    "qualifiedLeads" INTEGER NOT NULL DEFAULT 0,

    CONSTRAINT "Job_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "JobCentral" (
    "id" SERIAL NOT NULL,
    "scriptType" TEXT NOT NULL,
    "params" JSONB NOT NULL,
    "queueName" TEXT NOT NULL,
    "clientId" INTEGER NOT NULL,
    "priority" INTEGER NOT NULL,
    "status" "JobStatus" NOT NULL DEFAULT 'PENDING',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "JobCentral_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Campaign" (
    "id" SERIAL NOT NULL,
    "campaign" TEXT NOT NULL,

    CONSTRAINT "Campaign_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AboutData" (
    "id" SERIAL NOT NULL,
    "companyId" INTEGER NOT NULL,
    "website" TEXT NOT NULL,
    "aboutData" TEXT NOT NULL,
    "homepageData" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "aboutUrls" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AboutData_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AmazonProductData" (
    "id" SERIAL NOT NULL,
    "data" JSONB NOT NULL DEFAULT '{}',
    "companyId" INTEGER NOT NULL,
    "status" TEXT NOT NULL DEFAULT '',
    "searchUrl" TEXT NOT NULL DEFAULT '',
    "slug" TEXT NOT NULL DEFAULT '',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "validTill" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "AmazonProductData_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CompetitionData" (
    "id" SERIAL NOT NULL,
    "data" TEXT NOT NULL,
    "companyId" INTEGER NOT NULL,
    "status" TEXT NOT NULL DEFAULT '',
    "compKeyPrompt" TEXT NOT NULL DEFAULT '',
    "searchKeyword" TEXT NOT NULL DEFAULT '',
    "prospectProductName" TEXT NOT NULL DEFAULT '',
    "prospectRevenue" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "competitorRevenue" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "competitorProductName" TEXT NOT NULL DEFAULT '',
    "competitorName" TEXT NOT NULL DEFAULT '',
    "competitorProductAmazonURL" TEXT NOT NULL DEFAULT '',
    "prospectProductAmazonURL" TEXT NOT NULL DEFAULT '',
    "prospectRevenueSource" TEXT NOT NULL DEFAULT '',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "CompetitionData_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PPCData" (
    "id" SERIAL NOT NULL,
    "companyId" INTEGER NOT NULL,
    "slug" TEXT NOT NULL DEFAULT '',
    "BrandedSearch" JSONB NOT NULL DEFAULT '{}',
    "BrandedKeywordSearch" JSONB NOT NULL DEFAULT '{}',
    "nonBrandedKeywordSearch" JSONB NOT NULL DEFAULT '{}',
    "ProspectPDPImage" JSONB NOT NULL DEFAULT '{}',
    "CompetitorSearch" JSONB NOT NULL DEFAULT '{}',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PPCData_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "OutputData" (
    "id" SERIAL NOT NULL,
    "companyName" TEXT NOT NULL,
    "firstName" TEXT NOT NULL,
    "secondName" TEXT NOT NULL DEFAULT '',
    "position" TEXT NOT NULL DEFAULT '',
    "email" TEXT NOT NULL DEFAULT '',
    "website" TEXT NOT NULL,
    "mailData" TEXT NOT NULL DEFAULT '',
    "promptTokens" INTEGER NOT NULL DEFAULT 0,
    "completionTokens" INTEGER NOT NULL DEFAULT 0,
    "aboutDataStatus" TEXT NOT NULL DEFAULT 'pending',
    "amazonDataStatus" TEXT NOT NULL DEFAULT 'pending',
    "homepageDataStatus" TEXT NOT NULL DEFAULT 'not-found',
    "qualificationStatus" TEXT NOT NULL DEFAULT '',
    "amazonSearchUrl" TEXT NOT NULL DEFAULT '',
    "promptTemplate" TEXT NOT NULL DEFAULT '',
    "userPrompt" JSONB NOT NULL DEFAULT '{}',
    "gptDetails" TEXT NOT NULL DEFAULT '',
    "inputPrice" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "outputPrice" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "prospectDetails" JSONB NOT NULL DEFAULT '{}',
    "compKeyPrompt" TEXT NOT NULL DEFAULT '',
    "searchKeyword" TEXT NOT NULL DEFAULT '',
    "competitorDetails" JSONB NOT NULL DEFAULT '{}',
    "revenueDifference" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "amazonAudit" JSONB NOT NULL DEFAULT '{}',
    "auditReport" JSONB NOT NULL DEFAULT '{}',
    "companySlug" TEXT NOT NULL DEFAULT '{}',
    "productSlug" TEXT NOT NULL DEFAULT '',
    "jobId" INTEGER NOT NULL,
    "companyId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "caseStudies" JSONB NOT NULL DEFAULT '{}',
    "category" JSONB NOT NULL DEFAULT '{}',
    "sellerDetails" JSONB NOT NULL DEFAULT '{}',
    "finalData" JSONB NOT NULL DEFAULT '{}',
    "ppcAudit" JSONB NOT NULL DEFAULT '{}',
    "auditMailData" JSONB NOT NULL DEFAULT '{}',
    "campaignName" TEXT NOT NULL DEFAULT '',
    "pricing" JSONB NOT NULL DEFAULT '{}',

    CONSTRAINT "OutputData_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AmazonAuditReport" (
    "id" SERIAL NOT NULL,
    "companyName" TEXT NOT NULL,
    "companyId" INTEGER NOT NULL,
    "sellerEmail" TEXT NOT NULL DEFAULT '',
    "productUrl" TEXT NOT NULL DEFAULT '',
    "amazonAudit" JSONB NOT NULL DEFAULT '{}',
    "auditReport" JSONB NOT NULL DEFAULT '{}',
    "isProductURLProvided" BOOLEAN NOT NULL DEFAULT false,
    "slug" TEXT NOT NULL DEFAULT '',
    "finalUrl" TEXT NOT NULL DEFAULT '',
    "pageImage" TEXT NOT NULL DEFAULT '',
    "productImage" TEXT NOT NULL DEFAULT '',
    "pdfUrl" TEXT NOT NULL DEFAULT '',
    "category" TEXT NOT NULL DEFAULT '',
    "competitorUrl" TEXT NOT NULL DEFAULT '',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "caseStudies" JSONB NOT NULL DEFAULT '{}',
    "prospectDetails" JSONB NOT NULL DEFAULT '{}',

    CONSTRAINT "AmazonAuditReport_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UTMSync" (
    "id" SERIAL NOT NULL,
    "uuid" TEXT NOT NULL,
    "url" TEXT NOT NULL,
    "clientName" TEXT NOT NULL,
    "sellerId" TEXT NOT NULL DEFAULT '',
    "type" TEXT NOT NULL DEFAULT '',
    "source" TEXT NOT NULL DEFAULT 'web',
    "campaign" TEXT NOT NULL DEFAULT 'default',

    CONSTRAINT "UTMSync_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UTMSyncNew" (
    "id" SERIAL NOT NULL,
    "uuid" TEXT NOT NULL,
    "url" TEXT NOT NULL,
    "redirectUrl" TEXT NOT NULL,
    "clientName" TEXT NOT NULL,
    "sellerId" TEXT NOT NULL DEFAULT '',
    "type" TEXT NOT NULL DEFAULT '',
    "campaign" TEXT NOT NULL DEFAULT '',
    "email" TEXT NOT NULL DEFAULT '',

    CONSTRAINT "UTMSyncNew_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "LexImageGenJob" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "clientId" INTEGER NOT NULL,
    "status" "JobStatus" NOT NULL DEFAULT 'PENDING',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "LexImageGenJob_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "LexImageGenReview" (
    "id" SERIAL NOT NULL,
    "reviewId" TEXT NOT NULL,
    "reviewUrl" TEXT NOT NULL,
    "asin" TEXT,
    "brandName" TEXT,
    "violationTagId" INTEGER[],
    "violationTag" TEXT[],
    "status" "JobStatus" NOT NULL DEFAULT 'PENDING',
    "imageUrl" TEXT NOT NULL DEFAULT '',
    "inputData" JSONB NOT NULL DEFAULT '{}',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "LexImageGenReview_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "LexImageGenOutputData" (
    "id" SERIAL NOT NULL,
    "jobId" INTEGER NOT NULL,
    "revId" INTEGER NOT NULL,
    "reviewUrl" TEXT NOT NULL,
    "violationTagId" INTEGER[],
    "violationTag" TEXT[],
    "asin" TEXT,
    "brandName" TEXT,
    "imageUrl" TEXT NOT NULL DEFAULT '',
    "status" "JobStatus" NOT NULL DEFAULT 'PENDING',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "LexImageGenOutputData_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "LexViolationTag" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "imageUrl" TEXT NOT NULL,
    "description" TEXT NOT NULL DEFAULT '',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "LexViolationTag_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "LexJob" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "type" "LexJobType" NOT NULL,
    "status" "JobStatus" NOT NULL DEFAULT 'PENDING',
    "errorMessage" TEXT,
    "countryCode" TEXT NOT NULL DEFAULT 'US',
    "sellerId" TEXT,
    "asin" TEXT,
    "totalAsins" INTEGER,
    "totalReviews" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "LexJob_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "LexSeller" (
    "id" SERIAL NOT NULL,
    "sellerId" TEXT NOT NULL,
    "name" TEXT,
    "countryCode" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "LexSeller_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "LexASIN" (
    "id" SERIAL NOT NULL,
    "asin" TEXT NOT NULL,
    "LexSellerId" INTEGER,
    "sellerName" TEXT,
    "title" TEXT,
    "category" TEXT,
    "image" TEXT,
    "productLink" TEXT,
    "reviewCounts" JSONB,
    "avgRating" DOUBLE PRECISION,
    "totalReviews" INTEGER,
    "status" "AsinStatus" NOT NULL DEFAULT 'PENDING',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "LexASIN_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "LexReview" (
    "id" SERIAL NOT NULL,
    "asin" TEXT NOT NULL,
    "jobId" INTEGER,
    "reviewContent" TEXT,
    "reviewTitle" TEXT,
    "reviewScore" DOUBLE PRECISION,
    "reviewDate" TIMESTAMP(3),
    "reviewer" TEXT,
    "reviewerCountry" TEXT,
    "reviewerID" TEXT,
    "isVerified" BOOLEAN,
    "reviewLink" TEXT,
    "reviewerLink" TEXT,
    "HelpfulCounts" INTEGER,
    "reviewImage" TEXT,
    "image1" TEXT,
    "image2" TEXT,
    "image3" TEXT,
    "image4" TEXT,
    "pageUrl" TEXT,
    "reviewID" TEXT,
    "sellerId" TEXT,
    "productTitle" TEXT,
    "productLink" TEXT,
    "variant_0" TEXT,
    "variant_1" TEXT,
    "status" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "prompt1Output" TEXT,
    "prompt2Output" TEXT,
    "prompt3Output" TEXT,
    "violation" BOOLEAN,

    CONSTRAINT "LexReview_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "LexReviewScraperCookies" (
    "id" SERIAL NOT NULL,
    "emailId" TEXT NOT NULL,
    "cookieKey" TEXT NOT NULL,
    "cookieStatus" "CookieStatus" NOT NULL,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "LexReviewScraperCookies_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_LexImageGenJobToLexImageGenReview" (
    "A" INTEGER NOT NULL,
    "B" INTEGER NOT NULL,

    CONSTRAINT "_LexImageGenJobToLexImageGenReview_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_JobAsins" (
    "A" INTEGER NOT NULL,
    "B" INTEGER NOT NULL,

    CONSTRAINT "_JobAsins_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE UNIQUE INDEX "User_email_key" ON "User"("email");

-- CreateIndex
CREATE INDEX "User_email_idx" ON "User"("email");

-- CreateIndex
CREATE INDEX "Configurations_clientId_idx" ON "Configurations"("clientId");

-- CreateIndex
CREATE INDEX "Company_jobId_idx" ON "Company"("jobId");

-- CreateIndex
CREATE INDEX "Job_status_idx" ON "Job"("status");

-- CreateIndex
CREATE INDEX "Job_clientId_idx" ON "Job"("clientId");

-- CreateIndex
CREATE INDEX "Job_campaignId_idx" ON "Job"("campaignId");

-- CreateIndex
CREATE INDEX "JobCentral_scriptType_idx" ON "JobCentral"("scriptType");

-- CreateIndex
CREATE INDEX "JobCentral_clientId_idx" ON "JobCentral"("clientId");

-- CreateIndex
CREATE INDEX "AboutData_companyId_idx" ON "AboutData"("companyId");

-- CreateIndex
CREATE INDEX "AmazonProductData_companyId_idx" ON "AmazonProductData"("companyId");

-- CreateIndex
CREATE INDEX "AmazonProductData_slug_idx" ON "AmazonProductData"("slug");

-- CreateIndex
CREATE INDEX "CompetitionData_companyId_idx" ON "CompetitionData"("companyId");

-- CreateIndex
CREATE INDEX "PPCData_companyId_idx" ON "PPCData"("companyId");

-- CreateIndex
CREATE INDEX "OutputData_jobId_idx" ON "OutputData"("jobId");

-- CreateIndex
CREATE INDEX "OutputData_companyId_idx" ON "OutputData"("companyId");

-- CreateIndex
CREATE INDEX "OutputData_productSlug_idx" ON "OutputData"("productSlug");

-- CreateIndex
CREATE INDEX "AmazonAuditReport_companyId_idx" ON "AmazonAuditReport"("companyId");

-- CreateIndex
CREATE INDEX "AmazonAuditReport_slug_idx" ON "AmazonAuditReport"("slug");

-- CreateIndex
CREATE INDEX "UTMSync_type_clientName_idx" ON "UTMSync"("type", "clientName");

-- CreateIndex
CREATE UNIQUE INDEX "UTMSyncNew_uuid_key" ON "UTMSyncNew"("uuid");

-- CreateIndex
CREATE INDEX "UTMSyncNew_type_clientName_idx" ON "UTMSyncNew"("type", "clientName");

-- CreateIndex
CREATE UNIQUE INDEX "LexImageGenReview_reviewId_key" ON "LexImageGenReview"("reviewId");

-- CreateIndex
CREATE UNIQUE INDEX "LexJob_name_key" ON "LexJob"("name");

-- CreateIndex
CREATE INDEX "LexJob_type_idx" ON "LexJob"("type");

-- CreateIndex
CREATE INDEX "LexJob_status_idx" ON "LexJob"("status");

-- CreateIndex
CREATE INDEX "LexJob_sellerId_idx" ON "LexJob"("sellerId");

-- CreateIndex
CREATE INDEX "LexJob_asin_idx" ON "LexJob"("asin");

-- CreateIndex
CREATE INDEX "LexJob_createdAt_idx" ON "LexJob"("createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "LexSeller_sellerId_key" ON "LexSeller"("sellerId");

-- CreateIndex
CREATE INDEX "LexSeller_sellerId_idx" ON "LexSeller"("sellerId");

-- CreateIndex
CREATE INDEX "LexSeller_countryCode_idx" ON "LexSeller"("countryCode");

-- CreateIndex
CREATE UNIQUE INDEX "LexASIN_asin_key" ON "LexASIN"("asin");

-- CreateIndex
CREATE INDEX "LexASIN_asin_idx" ON "LexASIN"("asin");

-- CreateIndex
CREATE INDEX "LexASIN_reviewCounts_idx" ON "LexASIN"("reviewCounts");

-- CreateIndex
CREATE INDEX "LexASIN_LexSellerId_idx" ON "LexASIN"("LexSellerId");

-- CreateIndex
CREATE INDEX "LexASIN_totalReviews_idx" ON "LexASIN"("totalReviews");

-- CreateIndex
CREATE UNIQUE INDEX "LexReview_reviewID_key" ON "LexReview"("reviewID");

-- CreateIndex
CREATE INDEX "LexReview_reviewID_idx" ON "LexReview"("reviewID");

-- CreateIndex
CREATE INDEX "LexReview_reviewScore_idx" ON "LexReview"("reviewScore");

-- CreateIndex
CREATE INDEX "LexReview_violation_idx" ON "LexReview"("violation");

-- CreateIndex
CREATE INDEX "LexReview_sellerId_idx" ON "LexReview"("sellerId");

-- CreateIndex
CREATE INDEX "LexReview_asin_idx" ON "LexReview"("asin");

-- CreateIndex
CREATE INDEX "_LexImageGenJobToLexImageGenReview_B_index" ON "_LexImageGenJobToLexImageGenReview"("B");

-- CreateIndex
CREATE INDEX "_JobAsins_B_index" ON "_JobAsins"("B");

-- AddForeignKey
ALTER TABLE "Configurations" ADD CONSTRAINT "Configurations_clientId_fkey" FOREIGN KEY ("clientId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Company" ADD CONSTRAINT "Company_clientId_fkey" FOREIGN KEY ("clientId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Company" ADD CONSTRAINT "Company_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "Job"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Job" ADD CONSTRAINT "Job_campaignId_fkey" FOREIGN KEY ("campaignId") REFERENCES "Campaign"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Job" ADD CONSTRAINT "Job_clientId_fkey" FOREIGN KEY ("clientId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AboutData" ADD CONSTRAINT "AboutData_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AmazonProductData" ADD CONSTRAINT "AmazonProductData_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CompetitionData" ADD CONSTRAINT "CompetitionData_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PPCData" ADD CONSTRAINT "PPCData_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OutputData" ADD CONSTRAINT "OutputData_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OutputData" ADD CONSTRAINT "OutputData_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "Job"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AmazonAuditReport" ADD CONSTRAINT "AmazonAuditReport_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LexJob" ADD CONSTRAINT "LexJob_asin_fkey" FOREIGN KEY ("asin") REFERENCES "LexASIN"("asin") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LexJob" ADD CONSTRAINT "LexJob_sellerId_fkey" FOREIGN KEY ("sellerId") REFERENCES "LexSeller"("sellerId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LexASIN" ADD CONSTRAINT "LexASIN_LexSellerId_fkey" FOREIGN KEY ("LexSellerId") REFERENCES "LexSeller"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LexReview" ADD CONSTRAINT "LexReview_asin_fkey" FOREIGN KEY ("asin") REFERENCES "LexASIN"("asin") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LexReview" ADD CONSTRAINT "LexReview_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "LexJob"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_LexImageGenJobToLexImageGenReview" ADD CONSTRAINT "_LexImageGenJobToLexImageGenReview_A_fkey" FOREIGN KEY ("A") REFERENCES "LexImageGenJob"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_LexImageGenJobToLexImageGenReview" ADD CONSTRAINT "_LexImageGenJobToLexImageGenReview_B_fkey" FOREIGN KEY ("B") REFERENCES "LexImageGenReview"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_JobAsins" ADD CONSTRAINT "_JobAsins_A_fkey" FOREIGN KEY ("A") REFERENCES "LexASIN"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_JobAsins" ADD CONSTRAINT "_JobAsins_B_fkey" FOREIGN KEY ("B") REFERENCES "LexJob"("id") ON DELETE CASCADE ON UPDATE CASCADE;
