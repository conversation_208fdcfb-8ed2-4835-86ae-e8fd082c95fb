-- CreateTable
CREATE TABLE "LexPromptChain" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "prompt1" TEXT NOT NULL,
    "prompt2" TEXT NOT NULL,
    "prompt3" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "isPrimary" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "LexPromptChain_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "LexPromptChain_isActive_idx" ON "LexPromptChain"("isActive");

-- CreateIndex
CREATE INDEX "LexPromptChain_isPrimary_idx" ON "LexPromptChain"("isPrimary");
