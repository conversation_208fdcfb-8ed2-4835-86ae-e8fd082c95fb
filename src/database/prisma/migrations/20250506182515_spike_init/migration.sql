-- CreateTable
CREATE TABLE "Feature" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "numOfVersions" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Feature_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Prompt" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "featureId" INTEGER NOT NULL,
    "content" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Prompt_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PromptLogs" (
    "id" SERIAL NOT NULL,
    "ftID" INTEGER NOT NULL,
    "promptId" INTEGER NOT NULL,
    "cost" DOUBLE PRECISION NOT NULL,
    "output" TEXT NOT NULL,
    "tokenUsage" JSONB,
    "email" TEXT,
    "sellerName" TEXT,
    "productSlug" TEXT,
    "clientId" INTEGER NOT NULL,

    CONSTRAINT "PromptLogs_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Feature_name_key" ON "Feature"("name");

-- CreateIndex
CREATE UNIQUE INDEX "Prompt_featureId_name_key" ON "Prompt"("featureId", "name");

-- AddForeignKey
ALTER TABLE "Prompt" ADD CONSTRAINT "Prompt_featureId_fkey" FOREIGN KEY ("featureId") REFERENCES "Feature"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
