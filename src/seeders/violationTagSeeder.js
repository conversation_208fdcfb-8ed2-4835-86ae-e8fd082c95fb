// src/seeders/violationTagSeeder.js
const fs = require("fs");
const csv = require("csv-parser");
const path = require("path");
const axios = require("axios");
const { uploadImage } = require("../services/aws/s3/index.js"); // adjust path if needed
const { PrismaClient } = require("@prisma/client");


const prisma = new PrismaClient();

function extractFileId(googleDriveLink) {
  const match = googleDriveLink.match(/\/d\/(.*?)\//);
  return match ? match[1] : null;
}

function getDirectDownloadLink(googleDriveLink) {
  const fileId = extractFileId(googleDriveLink);
  return fileId
    ? `https://drive.google.com/uc?export=download&id=${fileId}`
    : null;
}

async function downloadImageBuffer(url) {
  const response = await axios.get(url, { responseType: "arraybuffer" });
  return response.data;
}


async function clearExistingTags() {
  try {
    await prisma.lexViolationTag.deleteMany();
    console.log("Cleared existing LexViolationTag data.");
  } catch (error) {
    console.error("Error clearing LexViolationTag table:", error);
  }
}

async function processCSV() {
  const results = [];

  fs.createReadStream(
    "/Users/<USER>/Documents/Work/DN-Intern/jeff-core-old/sample/violation_tag.csv"
  )
    .pipe(csv())
    .on("data", (data) => results.push(data))
    .on("end", async () => {
      for (const row of results) {
        const name = row["Violation Tag"];
        const description = row.Guideline;
        const driveLink = row.Image;
        const imageKey = `${row["Violation Tag"]}.jpg`;

        const imageUrl = getDirectDownloadLink(driveLink);
        if (!imageUrl) {
          console.error(`Invalid Drive link for ${name}`);
          continue;
        }

        try {
          const imageBuffer = await downloadImageBuffer(imageUrl);
          const bucketName = "eq-lex";
          const imageFolder = "violation_tags/";
          const image = await uploadImage(imageBuffer, imageKey);
          console.log({ image });
          const finalS3Url = `https://${bucketName}.s3.amazonaws.com/${imageFolder}${imageKey}`;

          await prisma.lexViolationTag.create({
            data: {
              name,
              imageUrl: finalS3Url,
              description,
            },
          });

          console.log(`Saved ${name} with image ${imageKey}`);
        } catch (err) {
          console.error(`Failed for ${name}`, err);
        }
      }

      await prisma.$disconnect();
    });
}

// processCSV();
// clearExistingTags();


async function testDownloadAndSaveImage() {
  const directUrl =
    "https://drive.google.com/uc?export=download&id=1hWTUNPbkJUviXXOxroxQx0Vc7ZEDBzZw";

  try {
    const response = await axios.get(directUrl, {
      responseType: "arraybuffer",
    });

    const contentType = response.headers["content-type"];
    console.log("🔍 Content-Type:", contentType);

    if (contentType.includes("image")) {
      fs.writeFileSync("test-image.jpg", response.data);
      console.log("✅ Image downloaded and saved as test-image.jpg");
    } else {
      fs.writeFileSync("response.html", response.data);
      console.log(
        "⚠️ Got non-image content. Saved as response.html for inspection."
      );
    }
  } catch (err) {
    console.error("❌ Error downloading image:", err.message);
  }
}

// 
// 
// testDownloadAndSaveImage();


// puppeteerDownload.js
const puppeteer = require("puppeteer");

async function downloadFromDrive(googleLink, filename) {
  const browser = await puppeteer.launch({headless:false});
  const page = await browser.newPage();

  await page.goto(googleLink, { waitUntil: "networkidle2" });

  // Wait for download button to appear
  const downloadButtonSelector = ".ndfHFb-c4YZDc-j7LFlb-bN97Pc";
  try {
    await page.waitForSelector(downloadButtonSelector, { timeout: 5000 });

    const downloadUrl = await page.$eval(downloadButtonSelector, el => el.href);
    console.log("Found real download URL:", downloadUrl);

    const viewSource = await page.goto(downloadUrl);
    const buffer = await viewSource.buffer();
    fs.writeFileSync(filename, buffer);
    console.log("✅ Image downloaded:", filename);
  } catch (e) {
    console.error("❌ Could not find download link. Try a different Drive setting.");
  }

  await browser.close();
}

// downloadFromDrive(
//   "https://drive.google.com/file/d/1hWTUNPbkJUviXXOxroxQx0Vc7ZEDBzZw/view",
//   "image.jpg"
// );




async function seedFromCSV() {
  const csvFilePath = path.join("/Users/<USER>/Documents/Work/DN-Intern/jeff-core-old/sample/all-tags.csv");

  const stream = fs.createReadStream(csvFilePath).pipe(csv());

  for await (const row of stream) {
    const {
      id,
      name,
      imageUrl,
      description = '',
      createdAt,
      updatedAt,
    } = row;

    await prisma.lexViolationTag.upsert({
      where: { id: Number(id) },
      update: {},
      create: {
        id: Number(id),
        name,
        imageUrl,
        description,
        createdAt: new Date(createdAt),
        updatedAt: new Date(updatedAt),
      },
    });

    console.log(`Seeded: ${name}`);
  }
}

seedFromCSV()
  .then(() => {
    console.log('Seeding completed.');
    return prisma.$disconnect();
  })
  .catch((err) => {
    console.error('Seeding failed:', err);
    prisma.$disconnect();
    process.exit(1);
  });
