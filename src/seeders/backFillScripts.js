const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function backfillProspectDetails() {
  try {
    // Fetch unique productSlug values from the outputData table
    const uniqueProductSlugs = await prisma.outputData.findMany({
      distinct: ['productSlug'],
      select: {
        productSlug: true,
        prospectDetails: true,
      },
    });

    // Iterate over each record and update the amazonAudit table
    let count = 0;
    for (const record of uniqueProductSlugs) {
        count++;
        console.log(`Backfilling record ${count} of ${uniqueProductSlugs.length}`);
      if (record.prospectDetails) {
        await prisma.amazonAuditReport.updateMany({
          where: { slug: record.productSlug },
          data: { prospectDetails: record.prospectDetails },
        });
      }
    }

    console.log('Backfill completed successfully.');
  } catch (error) {
    console.error('Error during backfill:', error);
  } finally {
    await prisma.$disconnect();
  }
}

async function main() {
  await backfillProspectDetails();
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });