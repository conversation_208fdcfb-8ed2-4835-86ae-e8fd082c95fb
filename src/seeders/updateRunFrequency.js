const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();
async function updateRunFrequency() {

  
  try {
    console.log('Starting run_frequency update...');
    
    // Set run_frequency to 3 for client reviews
    const clientReviewsResult = await prisma.lexReview.updateMany({
      where: {
        isClient: true
      },
      data: {
        run_frequency: 3
      }
    });
    console.log(`Updated ${clientReviewsResult.count} client reviews with run_frequency = 3`);
    
    // Set run_frequency to 1 for client reviews with violations
    const clientViolationReviewsResult = await prisma.lexReview.updateMany({
      where: {
        isClient: true,
        violation: true
      },
      data: {
        run_frequency: 1
      }
    });
    console.log(`Updated ${clientViolationReviewsResult.count} client violation reviews with run_frequency = 1`);
    
    // Set run_frequency to null for all other cases
    const nullReviewsResult = await prisma.lexReview.updateMany({
      where: {
        OR: [
          { isClient: null },
          { isClient: false }
        ]
      },
      data: {
        run_frequency: 0
      }
    });
    console.log(`Updated ${nullReviewsResult.count} non-client reviews with run_frequency = null`);
    
    console.log('Run frequency update completed successfully!');
    
  } catch (error) {
    console.error('Error updating run_frequency:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Export the function
module.exports = { updateRunFrequency };

// If this file is run directly, execute the function
if (require.main === module) {
  updateRunFrequency()
    .then(() => {
      console.log('Seeder completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Seeder failed:', error);
      process.exit(1);
    });
} 