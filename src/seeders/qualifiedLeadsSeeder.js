const prisma = require("../database/prisma/getPrismaClient");

const seeder = async () => {
  try {
    const jobs = await prisma.job.findMany({
      include: {
        OutputData: {
          select: { mailData: true, productSlug: true, campaignName: true },
        },
      },
    });
    const getQualifiedLeadsCount = (outputData) => {
      return outputData.filter(
        (data) => data.mailData && data.mailData.length > 0
      ).length;
    };
    for (const job of jobs) {
      await prisma.job.update({
        where: {
          id: job.id,
        },
        data: {
          qualifiedLeads: getQualifiedLeadsCount(job.OutputData),
        },
      });
        console.log("Seeded Job ID:", job.id);
    }
  } catch (error) {
      console.log("ERROR:", error);
  }
};

seeder();