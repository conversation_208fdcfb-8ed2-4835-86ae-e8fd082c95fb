// Import the Prisma client
const { PrismaClient } = require("@prisma/client");
const { generateSlug } = require("../utils/generateSlug");
const prisma = new PrismaClient();

async function main() {
    // Get all amazonAuditReports
    const amazonAuditReports = await prisma.amazonAuditReport.findMany();
    // Loop through all the amazonAuditReports
    for (const report of amazonAuditReports) {
        // Update the slug of each report
        await prisma.amazonAuditReport.update({
            where: {
                id: report.id
            },
            data: {
                slug: generateSlug(report.companyName)
            }
        });
    }

    // Get all outputData
    const outputData = await prisma.outputData.findMany();
    // Loop through all the outputData
    for (const data of outputData) {
        // Update the slug of each data
        await prisma.outputData.update({
            where: {
                id: data.id
            },
            data: {
                productSlug : generateSlug(data.companyName)
            }
        });
    }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
