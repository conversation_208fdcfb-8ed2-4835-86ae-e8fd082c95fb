// Import the Prisma client
const { PrismaClient } = require("@prisma/client");
const { generateAuditPdf } = require("../utils/getAmazonAudit");
const prisma = new PrismaClient();

async function main() {
  // Get all outputData

  const outputData = await prisma.outputData.findMany();

  // Loop through all the outputData
  for (const data of outputData) {
    // Update the slug of each data
    // await generateAuditPdf(data.slug);
    if (data.mailData) {
      const auditReports = await prisma.amazonAuditReport.findFirst({
        where: {
          slug: data.productSlug,
        },
        select: {
          id: true,
        },
      });
      await prisma.amazonAuditReport.update({
        where: {
          id: auditReports.id,
        },
        data: {
          sellerEmail: data.mailData,
        },
      });
    }
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
