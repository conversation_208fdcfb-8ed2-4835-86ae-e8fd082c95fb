const jwt = require("jsonwebtoken");
const JWT_SECRET = process.env.JWT_SECRET;
function userAuth(req, res, next) {
  const authHeader = req.headers["authorization"];
  const token = authHeader && authHeader.split(" ")[1];
  if (!token) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    // console.log("User Type:", user);
    // console.log("Req Params:", req.params);
    // console.log("Error:", err);
    if (
      err ||
      (req.params.clientId &&
        req.params.clientId != user.userId &&
        user.userType !== "admin")
    ) {
      return res.status(403).json({ error: "Forbidden" });
    }
    req.user = user;
    next();
  });
}

function adminAuth(req, res, next) {
  const authHeader = req.headers["authorization"];
  const token = authHeader && authHeader.split(" ")[1];
  
  if (!token) {
    console.log("Admin auth failed: No token provided");
    return res.status(401).json({ error: "Unauthorized" });
  }
  
  // Check if the user is an admin
  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      console.log("Admin auth failed: Token verification error", err.name, err.message);
      return res.status(403).json({ error: "Forbidden - Invalid Token" });
    }
    
    if (user.userType !== "admin") {
      console.log("Admin auth failed: User is not an admin", user.userType);
      return res.status(403).json({ error: "Forbidden - Admin Access Required" });
    }
    
    req.user = user;
    next();
  });
}

module.exports = { userAuth, adminAuth };
