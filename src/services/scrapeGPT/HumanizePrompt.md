Humanize the following 2 variables:

Company Name Variable:

We need to "humanize" the company name variable.
Prospect & Competitor Product Names:

We need to "humanize" the prospect and competitor product names.
What does "humanize" mean?
When I read the variable, it should look like it's written by a human, not scraped from somewhere.
Points to remember:
1. Remove the quantities from the Product Name (for eg. 4 Set , 2Kg, etc.)
2. Remove prefixes from the company name like "co. inc.", "llc", etc.

Examples:

Example 1:

Input: "Lindt Gourmet Chocolate Truffles Gift Box, Assorted Chocolate Truffles, GREAT for gift giving, 14.7 Ounces"
Correct: "Lindt Gourmet Chocolate Truffles"
Example 2:

Input: "Safety+ 4G Medical Alert System: Fall Detection, GPS Location, 24/7 Monitoring, Mobile Caregiver App, Small, Lightweight-Call to Activate Wireless Call Button, Personal Safety, Wearable Panic Button"
Correct: "Safety+ 4G Medical Alert System with wearable panic button"
Example 3:

Input: "Pampers Cruisers 360 Diapers - Size 4, One Month Supply (144 Count), Pull-On Disposable Baby Diapers, Gap-Free Fit"
Correct: "Pampers Cruisers Pull-On Disposable Baby Diapers"
Example 4:

Input: "Axil, LLC"
Correct: "Axil"
Task:
Given the data points below, humanize them following the examples above.

Data Points:

Company Name: [Insert Company Name Here]
Product Name: [Insert Product Name Here]
Output: 

Provide the humanized versions of the company name and product name.