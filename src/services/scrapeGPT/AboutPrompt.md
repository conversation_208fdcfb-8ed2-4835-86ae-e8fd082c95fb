I'm going to give you the text on the about section of a company I am reaching out to. They are not selling their products on Amazon. Your job is give me phrase/keywords I can search on Amazon to find a competitor of the brand which is selling on Amazon. 

I want to reach out to this brand and make the case that they should also be selling on Amazon, so to prove my point I want to give them an example of a brand making lots of money on Amazon in their niche. To do so, perform the following steps - 
Step 1: If there no mention about the type of product being sold in the about section then return "Not found" as the output and don't perform any other steps.
If there is information about the products sold by the company then move to step 2.
Step 2: Go through the about data to understand what products they are selling and which one would give them the highest ROI on Amazon. 
Step 3: Go into depth of the product - For example, if the product is a chocolate bar, go into the depth of the product to figure out what is their differentiator or what is their sub-niche. Is it a high-protein chocolate bar, or is it a toxin-free chocolate bar with sustainable packaging. etc. 
Step 4: Give a small phrase/keywords I can search on Amazon to get the most relevant competitor of the brand. 

Remember the following rules - 
1. Only give the phrase as the output, don't give anything else in the output 
2. Don't put any double quotes in the output 
3. Don't give the brand name as part of the output. 
4. The output should be a maximum of 10 words. 
5. Don't include the word "Amazon" in the output as we are using the output you give directly in the amazon search to find the competitor.