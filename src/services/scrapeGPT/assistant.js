const { OpenAI } = require("openai");
const fs = require("fs");
const { sendErrorEmail } = require("../../utils/mailHelper");
const Sentry = require("@sentry/node");
const { getOpenApiKey } = require("../../models/configuration");
require("dotenv").config();
// Function to create a thread run with the specified parameters
async function run(json_string, clientId) {
  try {
    // const thread = await open_ai.beta.threads.create();
    // console.log(thread);
    const {apiKey, assistantId} = await getOpenApiKey(clientId);
    // Initialize the OpenAI instance with your API key
    const open_ai = new OpenAI({ apiKey: apiKey, default_headers: { "OpenAI-Beta": "assistants=v2" } });
    const assistant_id = assistantId;

    let run = await open_ai.beta.threads.createAndRun({
      assistant_id: assistant_id,
      thread: {
        messages: [{ role: "user", content: json_string }],
      },
    });

    // console.log("Thread run created:", run);
    while (["queued", "in_progress", "cancelling"].includes(run.status)) {
      console.log("Waiting for thread run to complete...", run.status);
      await new Promise((resolve) => setTimeout(resolve, 1000)); // Wait for 1 second
      run = await open_ai.beta.threads.runs.retrieve(run.thread_id, run.id);
    }
    console.log("Thread run completed:", run.status);
    result = run.usage;
    result["gptDetails"] = run.model;
    result["prompt"] = JSON.stringify(run.instructions);
    if (run.status === "completed") {
      const messages = await open_ai.beta.threads.messages.list(run.thread_id);
      // console.log("Messages:" + JSON.stringify(messages));
      for (const message of messages.data.reverse()) {
        // console.log(`${message.role} > ${message.content[0].text.value}`);
        if (message.role === "assistant") {
          result["message"] = message.content[0].text.value.replace(
            /"""/g,
            " "
          );
          return result;
        }
      }
    } else {
      console.log("Thread run failed:", run.last_error.message);
    }
    return result;
  } catch (error) {
    console.error("Error Stack:", error.stack);
    Sentry.captureException("Error in thread run", error);
    // if(error.response.data.error.code === 'insufficient_funds') {
    //   // sendErrorEmail("gptCreditError");
    //   console.log("Insufficient funds");
    // }
    // throw new Error("Error creating thread run: " + error);
  }
}

// Example usage
async function exampleUsage() {
  try {
    const json = fs.readFileSync("scrapeGPT/data.json", "utf8");
    const result = await run(JSON.stringify(json));
    console.log(result);
  } catch (error) {
    console.error("Error Stack:", error.stack);
    console.error("Error in exampleUsage:", error);
  }
}

// exampleUsage();
exports.run = run;
