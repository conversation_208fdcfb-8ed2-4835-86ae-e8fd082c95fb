const { AzureOpenAI } = require("openai");
const { GoogleGenAI } = require("@google/genai");
const { wrapOpenAI } = require("langsmith/wrappers");
const dotenv = require("dotenv");

dotenv.config();

/**
 * A cleaner, more modular multi-provider AI service that uses a centralized
 * configuration for different AI models, following the existing pattern of wrapping
 * native clients with Langsmith for tracing.
 */
class MultiProviderAI {
  constructor() {
    // --- Client Initialization ---

    // For Azure OpenAI
    const azureClient = new AzureOpenAI({
      apiKey: process.env.AZURE_OPENAI_API_KEY,
      endpoint: process.env.AZURE_OPENAI_ENDPOINT,
      apiVersion: process.env.AZURE_OPENAI_API_VERSION,
    });

    // For Gemini using new @google/genai library
    const geminiClient = new GoogleGenAI({
      apiKey: process.env.GEMINI_API_KEY,
    });

    // --- Model Configuration ---

    this.models = {
      "azure-gpt4o": {
        name: "Azure GPT-4o (Jeff)",
        provider: "azure-openai",
        client: wrapOpenAI(azureClient), // Wrapped client
        deployment: "gpt-4o-jeff",
      },
      "gpt-4.1-jeff": {
        name: "GPT-4.1 (Jeff)",
        provider: "azure-openai",
        client: wrapOpenAI(azureClient), // Wrapped client
        deployment: "gpt-4.1-jeff",
      },
      "gpt-4.1-mini-jeff": {
        name: "GPT-4.1 Mini (Jeff)",
        provider: "azure-openai",
        client: wrapOpenAI(azureClient), // Wrapped client
        deployment: "gpt-4.1-mini-jeff",
      },
      "gemini-2.5-flash": {
        name: "Gemini 2.5 Flash",
        provider: "gemini",
        client: geminiClient, // Native client
        deployment: "gemini-2.5-flash",
      },
      "gemini-2.5-pro": {
        name: "Gemini 2.5 Pro",
        provider: "gemini",
        client: geminiClient, // Native client
        deployment: "gemini-2.5-pro",
      },
    };

    // Add legacy aliases
    this.models["azure-openai"] = this.models["azure-gpt4o"];
    this.models["gemini"] = this.models["gemini-2.5-flash"];
    this.models["gemini-1.5-flash"] = this.models["gemini-2.5-flash"]; // Legacy alias for backward compatibility
  }

  getModel(modelId) {
    const model = this.models[modelId];
    if (!model) {
      throw new Error(`Unsupported model: ${modelId}.`);
    }
    return model;
  }

  async executePrompt(modelId, systemMessage, userMessage, options = {}) {
    const modelConfig = this.getModel(modelId);
    const { temperature = 0.7, maxTokens = 1500 } = options;
    // console.log(modelConfig, "model config");
    try {
      if (modelConfig.provider === "gemini") {
        console.log(systemMessage, userMessage, modelConfig, "model config");
        return this.executeGemini(
          modelConfig,
          systemMessage,
          userMessage,
          options
        );
      } else {
        return this.executeOpenAICompatible(
          modelConfig,
          systemMessage,
          userMessage,
          options
        );
      }
    } catch (error) {
      console.error(`Error executing prompt with ${modelId}:`, error);
      throw error;
    }
  }

  async executeOpenAICompatible(
    modelConfig,
    systemMessage,
    userMessage,
    options
  ) {
    const { client, deployment } = modelConfig;
    const response = await client.chat.completions.create({
      model: deployment,
      messages: [
        { role: "system", content: systemMessage },
        { role: "user", content: userMessage },
      ],
      temperature: options.temperature,
      max_tokens: options.maxTokens,
    });

    return {
      content: response.choices[0]?.message?.content || "",
      usage: {
        promptTokens: response.usage?.prompt_tokens || 0,
        completionTokens: response.usage?.completion_tokens || 0,
        totalTokens: response.usage?.total_tokens || 0,
      },
      model: modelConfig.name,
      provider: modelConfig.provider,
    };
  }

  async executeGemini(modelConfig, systemMessage, userMessage, options) {
    const { client, deployment } = modelConfig;

    try {
      console.log(`🤖 [Gemini] Executing prompt with model: ${deployment}`);
      console.log(`🤖 [Gemini] Options:`, {
        temperature: options.temperature,
        maxTokens: options.maxTokens,
      });
      console.log(
        `🤖 [Gemini] System message length: ${systemMessage.length} chars`
      );
      console.log(
        `🤖 [Gemini] User message length: ${userMessage.length} chars`
      );

      const prompt = `System: ${systemMessage}\n\nUser: ${userMessage}`;
      console.log(`🤖 [Gemini] Combined prompt length: ${prompt.length} chars`);
      console.log(
        `🤖 [Gemini] Combined prompt preview: ${prompt.substring(0, 200)}...`
      );

      // Use new @google/genai API format
      const response = await client.models.generateContent({
        model: deployment,
        contents: prompt,
      });

      const text = response.text;

      console.log(response, "response from gemini");
      console.log(
        `✅ [Gemini] Response received, length: ${text.length} chars`
      );
      console.log(`✅ [Gemini] Response preview: ${text.substring(0, 200)}...`);

      // Estimate tokens for Gemini
      const estimatedPromptTokens = Math.ceil(prompt.length / 4);
      const estimatedCompletionTokens = Math.ceil(text.length / 4);

      console.log(
        `📊 [Gemini] Token usage - Prompt: ${estimatedPromptTokens}, Completion: ${estimatedCompletionTokens}, Total: ${
          estimatedPromptTokens + estimatedCompletionTokens
        }`
      );

      return {
        content: text,
        usage: {
          promptTokens: estimatedPromptTokens,
          completionTokens: estimatedCompletionTokens,
          totalTokens: estimatedPromptTokens + estimatedCompletionTokens,
        },
        model: modelConfig.name,
        provider: modelConfig.provider,
      };
    } catch (error) {
      console.error(`❌ [Gemini] Error executing prompt:`, {
        errorMessage: error.message,
        errorStack: error.stack,
        errorCode: error.code || "NO_CODE",
        errorStatus: error.status || "NO_STATUS",
        model: deployment,
        systemMessageLength: systemMessage.length,
        userMessageLength: userMessage.length,
        options: options,
      });

      // Log additional error details if available
      if (error.response) {
        console.error(`❌ [Gemini] API Response Error:`, {
          status: error.response.status,
          statusText: error.response.statusText,
          data: error.response.data,
        });
      }

      if (error.request) {
        console.error(`❌ [Gemini] Request Error:`, {
          url: error.request.url,
          method: error.request.method,
          headers: error.request.headers,
        });
      }

      // Re-throw the error with additional context
      throw new Error(
        `Gemini API Error: ${error.message} (Model: ${deployment})`
      );
    }
  }

  replaceVariables(content, variables) {
    let result = content;
    for (const [key, value] of Object.entries(variables)) {
      result = result.replace(new RegExp(`{{${key}}}`, "g"), value || "");
    }
    return result;
  }

  getAvailableModels() {
    // Define the primary models we want to expose (exclude aliases)
    const primaryModels = [
      "azure-gpt4o",
      "gpt-4.1-jeff",
      "gpt-4.1-mini-jeff",
      "gemini-2.5-flash",
      "gemini-2.5-pro",
    ];

    return primaryModels.map((id) => ({
      id,
      name: this.models[id].name,
      provider: this.models[id].provider,
    }));
  }
}

module.exports = { MultiProviderAI };
