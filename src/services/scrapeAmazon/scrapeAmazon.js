const workerpool = require("workerpool");
const debug = require("debug")("worker-pool:");
require("dotenv").config();

const { getHtmlByProxy, getHtmlFromS3 } = require("../../utils/getHtmlByProxy");
const getProductAsins = require("../../utils/scrapeAmazonUtils/getProductAsins");
const {
  getProductDetails,
} = require("../../utils/scrapeAmazonUtils/getProductDetails");
const { MAX_NO_OF_PRODUCT_FETCH } = require("./constant");

const prisma = require("../../database/prisma/getPrismaClient");
const getAsinFromStoreFront = require("../../utils/scrapeAmazonUtils/getAsinFromStoreFront");
const getTargetURL = require("../../utils/multicountry/getTargetURL");
const getHtmlByRoyal = require("../../utils/getHtmlByRoyal");
const { generateSlug } = require("../../utils/generateSlug");

// Main function to scrape Amazon
async function scrapeAmazon({ company_name, company_id, clientId }) {
  const globalValidTill = new Date(process.env.GLOBAL_VALID_TILL || 0);
  console.log("Scraping Amazon for:", company_name, company_id);
  try {
    const TARGET_URL = await getTargetURL(company_id);
    let data = await getExistingAmazonData(company_id);
    if (data) {
      console.log("Data Valid Till:", data.validTill);
      console.log("Global Valid Till:", globalValidTill);
      console.log("Today's Date:", new Date());
    }
    if (
      !data ||
      data.status !== "completed" ||
      data.validTill < new Date() ||
      data.validTill < globalValidTill
    ) {
      console.log("Reprocessing Amazon Data for:", company_name);
      data = await processAmazonData({
        company_name,
        company_id,
        clientId,
        TARGET_URL,
      });
    } else {
      console.log("Amazon Data already exists for:", company_name);
    }
    return data;
  } catch (err) {
    handleError(err);
  }
}

// Check if data already exists in the database
async function getExistingAmazonData(company_id) {
  return await prisma.amazonProductData.findFirst({
    where: {
      companyId: company_id,
    },
  });
}

// Process Amazon data if not found or not completed
async function processAmazonData({
  company_name,
  company_id,
  clientId,
  TARGET_URL,
}) {
  debug("started #%s", company_name);

  const formatSearchKeyword = formatCompanyName(company_name);
  const searchUrl = `${TARGET_URL}/s?k=${formatSearchKeyword}`;

  try {
    const outputData = await prisma.outputData.findFirst({
      where: {
        companyId: company_id,
      },
    });

    let productAsin = await getFinalAsins(
      outputData,
      clientId,
      searchUrl,
      company_name,
      company_id
    );
    const amazonExistence = productAsin.length >= 1;
    console.log("ASINS:", productAsin);
    if (!amazonExistence) {
      console.log("No Product Found...")
      saveAmazonData({}, company_id, searchUrl, clientId, false);
      return;
    }

    const productsMap = await fetchProductDetails(
      productAsin,
      company_name,
      clientId,
      outputData,
      TARGET_URL,
      company_id
    );

    const { productDetails, storeFront } = extractProductData(productsMap);

    let finalData = createFinalData(
      company_name,
      amazonExistence,
      productDetails,
      storeFront
    );

    await saveAmazonData(finalData, company_id, searchUrl, clientId, true);
  } catch (err) {
    await handleErrorInProcessing(err, company_id, searchUrl);
  }
}

// Format company name to fit search query
function formatCompanyName(company_name) {
  return company_name
    .trim()
    .replace(/[^\w\s-]/g, "")
    .replace(/\s+/g, "+")
    .replace(/^-+|-+$/g, "");
}

function getSellerStoreFrontUrl(sellerUrl) {
  let domain = "";
  let sellerId = "";

  // Step 1: Extract the domain from the URL
  const domainRegex = /https?:\/\/(www\.)?amazon\.[a-z.]+/;
  const domainMatch = sellerUrl.match(domainRegex);
  if (domainMatch) {
    domain = domainMatch[0];
  }

  // Step 2: Extract the seller ID
  const sellerIdRegexMe = /me=([A-Za-z0-9]+)/;
  const sellerIdRegexSeller = /seller=([A-Za-z0-9]+)/;

  const sellerMatchMe = sellerUrl.match(sellerIdRegexMe);
  const sellerMatchSeller = sellerUrl.match(sellerIdRegexSeller);

  if (sellerMatchMe) {
    sellerId = sellerMatchMe[1];
  } else if (sellerMatchSeller) {
    sellerId = sellerMatchSeller[1];
  }

  // Step 3: Construct sellerStoreFrontUrl
  if (domain && sellerId) {
    const sellerStoreFrontUrl = `${domain}/s?me=${sellerId}`;
    return sellerStoreFrontUrl;
  }

  return null; // Return null if unable to construct the URL
}

// Get final ASINs from various sources (best-selling product URL, storefront URL, etc.)
async function getFinalAsins(
  outputData,
  clientId,
  searchUrl,
  company_name,
  company_id
) {
  let productAsin = [];

  if (
    outputData?.prospectDetails?.productAmazonURL ||
    outputData?.prospectDetails?.bestSellingProductURL
  ) {
    return extractAsinFromUrl(
      outputData?.prospectDetails?.productAmazonURL ||
        outputData?.prospectDetails?.bestSellingProductURL
    );
  } else if (outputData?.sellerDetails["Seller Storefront Link"]) {
    const storeFrontUrl = outputData?.sellerDetails["Seller Storefront Link"];
    const sellerUrl = getSellerStoreFrontUrl(storeFrontUrl);
    console.log({ sellerUrl });
    if (sellerUrl) {
      const storeFrontHtml = await getHtmlFromS3(
        sellerUrl,
        clientId,
        company_id
      );
      productAsin = getAsinFromStoreFront({
        htmlData: storeFrontHtml,
        numOfProducts: MAX_NO_OF_PRODUCT_FETCH,
      });
    } else {
      console.log("No StoreFront URL Found");
    }
  } else {
    const companySearchPageHTML = await getHtmlByProxy(searchUrl, clientId);
    productAsin = getProductAsins({
      htmlData: companySearchPageHTML,
      numOfProducts: MAX_NO_OF_PRODUCT_FETCH,
      keyword: company_name,
    });
  }

  return productAsin;
}

// Fetch product details by processing each ASIN
async function fetchProductDetails(
  productAsin,
  company_name,
  clientId,
  outputData,
  TARGET_URL,
  company_id
) {
  const productsMap = new Map();

  for (const asin of productAsin) {
    const data = await processAsin(
      asin,
      company_name,
      clientId,
      outputData,
      TARGET_URL,
      company_id
    );
    productsMap.set(asin, data);
  }

  return productsMap;
}

// Process a single ASIN and fetch its details
async function processAsin(
  asin,
  company_name,
  clientId,
  outputData,
  TARGET_URL,
  company_id
) {
  const productURL = `${TARGET_URL}/dp/${asin}?language=en_GB`;
  let productPageHTML = await getHtmlByProxy(productURL, clientId,3,company_id);
  // let productPageHTML = await getHtmlByRoyal(productURL, clientId, company_id);

  if (!productPageHTML || Object.keys(productPageHTML).length === 0) {
    // Handle faulty page
    console.log("Page with Current ASIN is Faulty! Fetching data again.");
    const storeFrontHtml = await getHtmlByProxy(
      outputData.prospectDetails.storeFrontURL,
      clientId
    );
    asin = getAsinFromStoreFront({
      htmlData: storeFrontHtml,
      numOfProducts: MAX_NO_OF_PRODUCT_FETCH,
    });
  }

  const productDetails = await getProductDetails(productPageHTML, company_name, productURL);
  
  return {
    product_details: productDetails,
    asin,
  };
}

// Extract product details and storefront information from the map
function extractProductData(productsMap) {
  const productDetails = [];
  let storeFront = {
    storefront_present: false,
    store_front_url: "N/A",
  };

  for (let product of productsMap.values()) {
    if (product?.product_details) {
      const { store, ...rest } = product.product_details;
      if (product.product_details.store) {
        storeFront = product.product_details.store;
      }
      productDetails.push({
        ...rest,
        asin: product.asin,
      });
    }
  }

  return { productDetails, storeFront };
}

// Create the final data structure to be saved
function createFinalData(
  company_name,
  amazonExistence,
  productDetails,
  storeFront
) {
  let finalData = {
    company_name,
    amazon_existence: amazonExistence,
    productData: productDetails,
  };

  if (amazonExistence) {
    finalData.store = storeFront;
  }

  return finalData;
}

// Save the final data to the database
async function saveAmazonData(
  finalData,
  company_id,
  searchUrl,
  clientId,
  amazonExistence
) {
  console.log("Saving Amazon Data to DB");
  let status = amazonExistence
    ? "completed"
    : "No products found for the specified brand.";

  let existingData = await prisma.amazonProductData.findFirst({
    where: {
      companyId: company_id,
    },
  });
  const validTill = new Date(new Date().getTime() + 7 * 24 * 60 * 60 * 1000);

  const slug = amazonExistence
    ? generateSlug(
        finalData.company_name +
          " " +
          finalData.productData[0].asin +
          " " +
          clientId
      )
    : "";
  if (existingData) {
    await prisma.amazonProductData.update({
      where: {
        id: existingData.id,
      },
      data: {
        data: finalData,
        companyId: company_id,
        status,
        slug,
        searchUrl,
        validTill,
        pushedToSB: false, // Reset flag when data is updated
      },
    });
  } else {
    await prisma.amazonProductData.create({
      data: {
        data: finalData,
        companyId: company_id,
        status,
        slug,
        searchUrl,
        validTill,
      },
    });
  }
}

// Handle errors during the main process
async function handleErrorInProcessing(err, company_id, searchUrl) {
  console.error("Error Stack:", err.stack);
  console.error("An error occurred:", err.message);

  const existingData = await prisma.amazonProductData.findFirst({
    where: {
      companyId: company_id,
    },
  });

  if (!existingData) {
    const status = err.message.includes(
      "403 You have used up all your API credits."
    )
      ? "proxy_error : Used up API credits"
      : "proxy_error";

    await prisma.amazonProductData.create({
      data: {
        data: {},
        companyId: company_id,
        status,
        searchUrl,
        validTill: new Date(new Date().getTime() + 15 * 24 * 60 * 60 * 1000),
      },
    });
  }
}

// General error handler
function handleError(err) {
  console.error("Error in scrapeAmazon:", err);
}

function extractAsinFromUrl(url) {
  const asinPart = url.split("/dp/")[1]; // Get the part after "/dp/"
  const asin = asinPart.split(/[/?]/)[0]; // Split by "?" or "/" and take the first part
  return [asin];
}

module.exports = scrapeAmazon;
