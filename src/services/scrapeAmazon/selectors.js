const selectors = {
  productLink: 'div[data-asin]:not([data-asin=""])',

  nameElement: "span#productTitle",
  nameElementAlt: "span#gc-asin-title",
  nameElementAlt2: "#title_feature_div span#title",

  priceWhole: ".a-price-whole",
  priceFraction: ".a-price-fraction",
  brandSelector: "#productOverview_feature_div > div > table > tbody > tr.a-spacing-small.po-brand > td.a-span9 > span",
  productTitle: ".a-size-base-plus.a-color-base.a-text-normal",
  brandHeader: ".a-size-mini.s-line-clamp-1 span",
  productContainer: '[data-component-type="s-search-result"]',

  sponsoredIcon: ".aok-inline-block.puis-sponsored-label-info-icon",

  bsrRowElement: "#productDetails_detailBullets_sections1",
  bsrHeaderElement: 'th:contains("Best Sellers Rank")',
  bsrListElement: 'li:contains("Best Sellers Rank:")',
  bsrRankElement: "ul.a-unordered-list.a-nostyle",

  numOfRatings: "span.a-size-base.s-underline-text",

  numOfRatingELement: ".a-size-base.s-underline-text",

  ratingElement: ".cm-cr-review-stars-spacing-big",
  numOfRating: "#acrCustomerReviewText",
  priceElement: ".a-price-whole",

  descriptionElement: "ul.a-unordered-list.a-vertical.a-spacing-mini",

  reviewsLink: "span#acrCustomerReviewLink", // we dont need this
  reviewsElement: ".review-text",

  runningAds: ".AdHolder",
  runningAds2:
    ".sg-col-20-of-24.s-result-item.sg-col-0-of-12.sg-col-16-of-20.s-widget.sg-col.s-widget-spacing-small.sg-col-12-of-16",

  storeLink: "#bylineInfo", //same for both

  sales: ".a-row.a-size-base span.a-size-base.a-color-secondary",
  lastMonthSale: ".social-proofing-faceout-title-text", //same

  aPlusContent1: "#aplusBrandStory_feature_div",
  aPlusContent2: ".aplus-v2",

  outOfStock: "#outOfStockBuyBox_feature_div", //Currently unavailable id taken
  categoryRank: "#productDetails_detailBullets_sections1",

  allProduct: 'div[data-cy="title-recipe"]',
  companyName: ".a-size-mini.s-line-clamp-1 span", //take innertext(works only on .com)
  //a-size-mini s-line-clamp-1

  //Reviews classes/Id:
  //Review when there is <see more review>
  allReviewsMain: "#cm_cr-review_list div.review",
  //when there is no <see more review>
  allReviewsAlt: "#cm-cr-dp-review-list div.review",
  allReviewsAlt2: ".customerReviewsMobileFeature",
  // numOfRating: ".acrCustomerReviewText",

  authorName: 'div[data-hook="genome-widget"] span.a-profile-name', //same for topReview
  authorNameAlt:
    ".customerReviewsMobileFeature .a-profile-content span.a-profile-name",

  reviewDate: 'span[data-hook="review-date"]', //same
  reviewTitle: ".review-title-content>span:not([class])", //same
  reviewBody: ".review-text-content>span",

  seeMoreReviews1: "#reviews-medley-footer a", // or .cr-widget-SeeAllReviews
  seeMoreReviews2: ".cr-widget-SeeAllReviews a",
  seeMoreReviewsAlt: 'a[data-hook="see-all-reviews-link-mobile"]',

  // totalReviewCount: 'span[data-hook="total-review-count"]',
  // totalReviewCount: 'span[data-hook="total-rating-count"]',

  reviewRating: 'i[data-hook="review-star-rating"]>span',

  //top review section
  //If using old view(mobile view), then you have to go to <see more reviews> always to get top reviews
  topReview: 'a[aria-label="All 5 star reviews"]', //get href from here
  topReviewAlt: "a.5star",

  alltopReview: "#cm_cr-review_list", //same

  topReviewBody: ".cr-full-content .review-text-sub-contents",
  //topReviewDate: 'span[data-hook="review-date"]>span:not([class])'
  brandName: ".po-brand",

  footer: ".nav-footer-line",
  searchBar: "#twotabsearchtextbox",
  searchSubmitButton: "#nav-search-submit-button",
  productTitleProductPage: "#productTitle",

  productVideo: "#videoCount",
  image: "img.s-image",
};
const Url = {
  web: "https://www.amazon.com",
};

module.exports = { selectors, Url };
