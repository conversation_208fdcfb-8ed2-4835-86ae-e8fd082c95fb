const prisma = require("../../../database/prisma/getPrismaClient");
const cron = require("node-cron");
const { ReviewCheckerStatus, ReviewJobStatus } = require("@prisma/client");
const { processReview, sendLexSlackNotification } = require("./utils");
const ResurrectedReviewHandler = require("./resurrectedReviewHandler");
require("dotenv").config();

const Bottleneck = require("bottleneck");
// Configure Bottleneck for concurrency and rate limiting
const limiter = new Bottleneck({
    maxConcurrent: 40, // Number of concurrent requests
    minTime: 100, // Minimum time between each request (ms)
});

async function processLexReviewCheckerJob(job) {
    try {

        // Update job status to 'processing'
        await prisma.lexReviewCheckerJob.update({
            where: { id: job.id },
            data: {
                status: ReviewCheckerStatus.PROCESSING,
            },
        });

        try {
            // const csvBuffer = await reviewChecker(job.inputFilePath);
            const pendingReviews = await prisma.lexReviewCheckerOutputData.findMany({
                where: {
                    reviewJobId: job.id,
                    status: {
                        in: [ReviewCheckerStatus.PENDING, ReviewCheckerStatus.FAILED],
                    },
                },
                include: {
                    review: true,
                },
            });

            console.log(`Processing ${pendingReviews.length} pending reviews for job ${job.id}`);

            const tasks = pendingReviews.map((review) =>
                limiter.schedule(async () => {
                    try {
                        const rawReviewStatus = await processReview(review.reviewUrl);
                        console.log("Raw Review Status:", rawReviewStatus);
                        console.log("Previous Review Status:", review.review.checkerStatus);

                        // Map string values to ReviewCheckerStatus
                        let reviewStatus;
                        if (rawReviewStatus === "REMOVED") {
                            reviewStatus = ReviewCheckerStatus.REMOVED;
                        } else if (rawReviewStatus === "PRESENT") {
                            reviewStatus = ReviewCheckerStatus.PRESENT;
                        } else {
                            reviewStatus = ReviewCheckerStatus.FAILED;
                        }

                        // Check if status transition is needed for resurrected reviews
                        const updatedReview = await ResurrectedReviewHandler.checkStatusTransition(review.review, reviewStatus);

                        if (
                            updatedReview.status === ReviewCheckerStatus.REMOVED &&
                            (review.review.checkerStatus === ReviewCheckerStatus.PRESENT ||
                                review.review.checkerStatus === ReviewCheckerStatus.PENDING)
                        ) {
                            await sendLexSlackNotification(updatedReview, "RESURRECTED");
                        }

                        await prisma.lexReview.update({
                            where: { id: review.revId },
                            data: {
                                checkerStatus: updatedReview.status,
                                removedAt: updatedReview.removedAt,
                                removedHistory: updatedReview.removedHistory || review.review.removedHistory,
                                returnedHistory: updatedReview.returnedHistory || review.review.returnedHistory,
                                comments: updatedReview.comments || review.review.comments,
                            },
                        });

                        await prisma.lexReviewCheckerOutputData.update({
                            where: { id: review.id },
                            data: { status: reviewStatus },
                        });

                        console.log(`Processed review ${review.id}: ${reviewStatus} (mapped from ${rawReviewStatus})`);
                    } catch (error) {
                        console.error(`Error processing review ${review.id}:`, error);
                        await prisma.lexReviewCheckerOutputData.update({
                            where: { id: review.id },
                            data: { status: ReviewCheckerStatus.FAILED },
                        });
                    }
                })
            );

            // Use Promise.allSettled to handle all tasks regardless of failures
            await Promise.allSettled(tasks);

            // Check remaining pending reviews
            const remaining = await prisma.lexReviewCheckerOutputData.count({
                where: {
                    reviewJobId: job.id,
                    status: {
                        in: [ReviewCheckerStatus.PENDING],
                    },
                },
            });

            console.log(`Job ${job.id} has ${remaining} remaining pending reviews`);

            // Mark as completed if no pending reviews remain (all have been attempted)
            if (remaining === 0) {
                await prisma.lexReviewCheckerJob.update({
                    where: { id: job.id },
                    data: {
                        status: ReviewJobStatus.COMPLETED,
                    },
                });
                console.log(`Job ${job.id} completed - all reviews attempted (some may have failed)`);
            } else {
                // Mark as failed if there are still pending reviews
                await prisma.lexReviewCheckerJob.update({
                    where: { id: job.id },
                    data: {
                        status: ReviewJobStatus.FAILED,
                    },
                });
                console.log(`Job ${job.id} failed - ${remaining} reviews still pending`);
            }
        } catch (error) {
            await prisma.lexReviewCheckerJob.update({
                where: { id: job.id },
                data: {
                    status: ReviewJobStatus.FAILED,
                },
            });
            console.error(`Job ${job.id} failed:`, error);
        }
    } catch (error) {
        console.error("Error in job processor:", error);
        prisma.lexReviewCheckerJob
            .update({
                where: { id: job.id },
                data: {
                    status: ReviewJobStatus.FAILED,
                },
            })
            .catch((err) => {
                console.error("Error updating job status:", err.message);
            });
    }
}
module.exports = processLexReviewCheckerJob;