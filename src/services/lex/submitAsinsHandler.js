const express = require("express");
const prisma = require("../../database/prisma/getPrismaClient");
const { scrapeDataFromAsins } = require("./scrapeDataFromAsins"); // Your new scraper function from previous
const { sendServerNotification } = require("../../utils/slack");

const router = express.Router();

const submitAsinsHandler = async (req, res) => {
  let { asins, countryCode = "US" } = req.body;

  if (!asins || (Array.isArray(asins) && asins.length === 0)) {
    return res.status(400).json({ error: "Missing ASIN(s)" });
  }
  if (!countryCode) {
    return res.status(400).json({ error: "Missing fields" });
  }

  // Ensure asins is an array
  if (!Array.isArray(asins)) {
    asins = [asins];
  }

  try {
    // Step 1: Scrape ASIN data
    const asinResults = await scrapeDataFromAsins(asins, countryCode);

    // Step 2: Upsert each ASIN in DB
    const savedAsins = [];

    const chunkSize = 20;
    for (let i = 0; i < asinResults.length; i += chunkSize) {
      const chunk = asinResults.slice(i, i + chunkSize);

      const results = await Promise.all(
        chunk.map((asinData) =>
          prisma.lexASIN.upsert({
            where: { asin: asinData.asin,countryCode: countryCode },
            update: { ...asinData },
            create: { ...asinData },
          })
        )
      );

      savedAsins.push(...results);
    }

    // Step 3: Send notification for ASIN list upload completion
    await sendServerNotification(
      "ASIN_UPLOAD_COMPLETE",
      `*Total ASINs:* ${asins.length}\n*Country:* ${countryCode}\n*Status:* Upload successful`
    );

    // Step 4: Return saved ASINs
    return res.status(200).json({
      message: "ASIN(s) submitted and saved successfully",
      asins: savedAsins,
    });
  } catch (err) {
    console.error("Error in POST /api/lex/asins:", err);
    return res.status(500).json({ error: "Internal server error" });
  }
};

module.exports = { submitAsinsHandler };
