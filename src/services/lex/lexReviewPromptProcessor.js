require("dotenv").config();
const { AzureOpenAI } = require("openai");
const createCsvWriter = require("csv-writer").createObjectCsvWriter;
const fs = require("fs").promises;
const path = require("path");
const {
  analyzeAmazonReviewsWithCompliance,
} = require("./lexPromptChainProcessor");

/**
 * Initialize AzureOpenAI client
 */
const client = new AzureOpenAI({
  apiKey: process.env.AZURE_OPENAI_API_KEY,
  endpoint: process.env.AZURE_OPENAI_ENDPOINT,
  deployment: process.env.AZURE_OPENAI_DEPLOYMENT,
  apiVersion: process.env.AZURE_OPENAI_API_VERSION,
});

/**
 * Analyze Amazon review content using AzureOpenAI
 * @param {Object} review - The original review object
 * @returns {Promise<string>}
 */
async function analyzeReviewContent(review) {
  const contentPrompt = `
You are an Amazon review expert. You are given review title, review content and product title/name as on Amazon.

INPUTS:
- Product Title: ${review.productTitle}
- Review Title: ${review["Review Title"]}
- Review Content: ${review.reviewContent}

Based on this: 
1. Carefully analyze the product category and type based on the title
2. Determine what constitutes relevant and expected content for reviews of this product type
3. Identify the main claims, opinions, or experiences described in the review
4. Consider multiple possible interpretations of ambiguous statements
5. Evaluate whether the review primarily discusses the product itself or other factors
6. Assess the reviewer's apparent intent and tone.

Give an output with the above answers in short but without missing a detail for further processing.
`;

  const completion = await client.chat.completions.create({
    messages: [
      {
        role: "system",
        content: "You are an Amazon review analysis assistant.",
      },
      {
        role: "user",
        content: contentPrompt,
      },
    ],
    model: process.env.AZURE_OPENAI_MODEL_ID,
    temperature: 0.7,
    max_tokens: 700,
  });

  return completion.choices?.[0]?.message?.content ?? "";
}

/**
 * Analyze review for compliance with community guidelines using AzureOpenAI
 * @param {Object} review - The original review object
 * @param {string} reviewAnalysis - Output from the first prompt
 * @returns {Promise<string>}
 */
async function analyzeReviewCompliance(review, reviewAnalysis) {
  const compliancePrompt = `
Analyze the following Amazon product review for compliance with community guidelines. For each guideline section, provide a confidence rating (0-3) indicating the likelihood of non-compliance:

0 = No violation
1 = Low confidence of violation (possibly non-compliant)
2 = Medium confidence of violation (likely non-compliant)
3 = High confidence of violation (clearly non-compliant)

INPUTS:
- Product Title: ${review.productTitle}
- Review Title: ${review["Review Title"]}
- Review Content: ${review.reviewContent}
- Review context: **${reviewAnalysis}**

DETAILED GUIDELINE SECTIONS:

G1 : Review focuses ONLY on Sellers and the Customer Service they provide
ALLOWED : "The product was good but customer service could be better" 
NOT ALLOWED : "Worst Customer service,nobody ever answers to mails" 
REASONING: Community content should help customers learn about the product itself, not individual ordering experiences

G2: Review focuses ONLY on Ordering issues and returns
ALLOWED : "The product is great but one of the products were missing"
NOT ALLOWED : "I received the wrong order and now I can't even return" 
REASONING: Community content should help customers learn about the product itself, not individual ordering experiences

G3: SHIPPING & PACKAGING
ALLOWED : "The product is okay, could have been packed better"
NOT ALLOWED: "Everything was spilling and damaged" "The product came broken, disappointed"
REASONING: Community content should help customers learn about the product itself, not individual ordering experiences

G4: PRODUCT CONDITION ON ARRIVAL: Review focuses ONLY on Product condition on arrival and damaged product at time of arrival
ALLOWED: Comments about the product's performance, quality, strength and features, If the review talks about the product along with the seller/shipping feedback.
NOT ALLOWED: "The box arrived damaged,"
REASONING: Community content should help customers learn about the product itself, not individual ordering experience

G5 :SHIPPING COST : Review focuses ONLY on Shipping cost and speed
ALLOWED : Product is great, shipping could have been faster "
NOT ALLOWED : "Seller took too long to ship,"
REASONING: Community content should help customers learn about the product itself, not individual ordering experiences

G6. PRICING/AVAILABILITY: Review contains individual pricing experiences or store-specific availability comments not relevant to all customers.
   ALLOWED: Value comments about the product (e.g., "For only $29, this blender is really great")
   ALLOWED: General availability wishes (e.g., "I wish this book was also available in paperback")
   NOT ALLOWED: Individual pricing experiences (e.g., "Found this item here for $5 less than at my local store")
   NOT ALLOWED: Store-specific availability (e.g., "My local Target doesn't carry this anymore")
   REASONING: These comments aren't relevant for all customers

G7. LANGUAGE VIOLATION: Content is written in languages other than English or Spanish or mixes languages inappropriately.
   ALLOWED: Content in the supported languages of the Amazon site
   NOT ALLOWED: Content in unsupported languages or mixed-language content
   REASONING: Content must be accessible to all users of the site

G8. SPAM/REPETITIVE CONTENT: Contains repetitive text, nonsense, gibberish, distracting punctuation/symbols, or ASCII art.
   ALLOWED: Coherent, non-repetitive text relevant to the product
   NOT ALLOWED: Repetitive text, nonsense/gibberish, content consisting mainly of punctuation/symbols, ASCII art
   REASONING: Such content is distracting and doesn't help customers make purchasing decisions

G9. PRIVATE INFORMATION: Shares personal information.
   NOT ALLOWED: Phone numbers, email addresses, mailing addresses, license plates, DSN, order numbers
   REASONING: Protects privacy and prevents identity theft

G10. PROFANITY/HARASSMENT: Contains profanity, obscenities, name-calling, harassment, threats, attacks, libel, defamation, or inflammatory content.
ALLOWED: Questioning beliefs or expertise respectfully, "I hate the product" "The product is crap"
NOT ALLOWED: Profanity/obscenities/name-calling, harassment/threats, attacks on people, libel/defamation/inflammatory content, coordinated posting from multiple accounts. Using Only abuse
REASONING: Maintains a respectful community environment

G11. HATE SPEECH: Expresses hatred based on protected characteristics or promotes organizations using hate speech.
   NOT ALLOWED: Hatred based on race, ethnicity, nationality, gender, gender identity, sexual orientation, religion, age, or disability
   NOT ALLOWED: Promoting organizations that use such hate speech
   REASONING: Ensures an inclusive and respectful environment

G12. SEXUAL CONTENT: Contains inappropriate sexual content.
   ALLOWED: Discussing sex and sensuality products sold on Amazon or products with sexual content
   NOT ALLOWED: Profanity/obscene language, content with nudity, sexually explicit images or descriptions
   REASONING: Maintains appropriate content standards

G13. EXTERNAL LINKS: Contains links to external sites, phishing, malware, or URLs with referrer/affiliate codes.
   ALLOWED: Links to other products on Amazon
   NOT ALLOWED: Links to external sites, phishing/malware sites, URLs with referrer tags or affiliate codes
   REASONING: Ensures user safety and prevents exploitation

G14. PROMOTIONAL CONTENT: Primary purpose is promoting a company, website, or special offer, or was created by someone with financial interest.
   NOT ALLOWED: Content whose MAIN purpose is promotion of another brand or product.
   REASONING: Prevents conflicts of interest and ensures authentic reviews

G15. ILLEGAL ACTIVITIES: Encourages illegal activities.
   NOT ALLOWED: Content encouraging violence, illegal drug use, underage drinking, child/animal abuse, fraud, terrorism
   NOT ALLOWED: Threats of physical/financial harm, fraudulent schemes, encouraging dangerous product misuse
   REASONING: Prevents promotion of harmful or illegal behavior

G16. MEDICAL CLAIMS: Makes statements about preventing or curing serious medical conditions.
   NOT ALLOWED: Claims related to preventing/curing serious medical conditions for any product type (including foods, beverages, supplements, cosmetics, personal care products)
   REASONING: Prevents potentially dangerous medical misinformation

Make sure not to hallucinate or generate content that is not there.

OUTPUT FORMAT:

REVIEW_INTERPRETATION: [Primary interpretation of the review's content and intent, noting any ambiguities or alternative readings]

RATINGS:
G1: [0-3]
G2: [0-3]
G3: [0-3]
G4: [0-3]
G5: [0-3]
G6: [0-3]
G7: [0-3]
G8: [0-3]
G9: [0-3]
G10: [0-3]
G11: [0-3]
G12: [0-3]
G13: [0-3]
G14: [0-3]
G15: [0-3]
G16: [0-3]

TOP_VIOLATIONS:
[ONLY if any violation rating above is more than or equal to 2, share the details below, else, List the three highest-rated violations in descending order by rating]

TOP1: G# - [Rating] - [Concise reasoning]
TOP2: G# - [Rating] - [Concise reasoning]
TOP3: G# - [Rating] - [Concise reasoning]

Make sure TOP_VIOLATIONS output format is clean and single quoted only, for example:

'TOP1: G10 - 2 - The review contains inflammatory content, including accus
ations of fraud ("absolute scam") and references to lawsuits, which could be con
sidered defamatory.  \n' +
'TOP2: G8 - 1 - The review includes some unclear or repetitive phrasing (e
.g., "Consciences of the tubes"), which may slightly detract from clarity but do
es not constitute outright gibberish.  \n' +
'TOP3: G6 - 1 - The review discusses pricing frustrations, but these are f
ramed as general concerns about value rather than individual pricing experiences
, making it a low-confidence violation.  ',
`;

  const completion = await client.chat.completions.create({
    messages: [
      {
        role: "system",
        content: "You are an Amazon review compliance analysis assistant.",
      },
      {
        role: "user",
        content: compliancePrompt,
      },
    ],
    model: process.env.AZURE_OPENAI_MODEL_ID,
    temperature: 0.3,
    max_tokens: 900,
  });

  return completion.choices?.[0]?.message?.content ?? "";
}

/**
 * Extract top violations from compliance output
 * @param {string} complianceOutput - Output from the compliance prompt
 * @returns {Object} - Object containing the extracted top violations
 */
function extractTopViolations(complianceOutput) {
  const topViolations = {
    GuidelineViolation1: "",
    GuidelineViolation2: "",
    GuidelineViolation3: "",
    ConfidenceScore1: 0,
    ConfidenceScore2: 0,
    ConfidenceScore3: 0,
    Reason1: "",
    Reason2: "",
    Reason3: "",
  };

  // This regex supports:
  // - Variants like "**TOP1**", "TOP1:", "TOP1 -"
  // - Dashes: -, –, --
  // - Any spacing issues
  // - Captures guideline, score, and reason robustly
  // const topRegex =
  //   /(?:\*+)?TOP\s*([1-3])(?:\*+)?\s*[:\-–]*\s*G(\d+)\s*[-–—]{1,2}\s*(\d+)\s*[-–—]{1,2}\s*(.+?)(?=(?:\n\s*(?:\*+)?TOP\s*[1-3])|\n*$)/gis;

  // for (const match of matches) {
  //   const index = parseInt(match[1]); // 1, 2, or 3
  //   const guideline = `G${match[2]}`;
  //   const score = parseInt(match[3], 10);
  //   const reason = match[4].trim().replace(/\*+$/, ""); // Remove trailing asterisks if any

  //   topViolations[`GuidelineViolation${index}`] = guideline;
  //   topViolations[`ConfidenceScore${index}`] = score;
  //   topViolations[`Reason${index}`] = reason;
  // }

  // Enhanced regex to handle multiple formats including the new format with quotes and line breaks:
  // - Original: "TOP1: G# - # - reason"
  // - Markdown: "- **TOP1:** G# - # - reason"
  // - New format: "'...TOP1: G# - # - reason...'"
  // - Variations with different spacing, punctuation, and line breaks
  const topRegex =
    /(?:[-*'"\s]*)?(?:\*+)?TOP\s*([1-3])(?:\*+)?(?::|-|\s)\s*G(\d+)\s*[-–—]{1,2}\s*(\d+)\s*[-–—]{1,2}\s*(.+?)(?=(?:\n.*?(?:[-*'"\s]*)?(?:\*+)?TOP\s*[1-3])|\n*$|'.*?\+|`.*?\+)/gis;

  const matches = [...complianceOutput.matchAll(topRegex)];

  for (const match of matches) {
    const index = parseInt(match[1]); // 1, 2, or 3
    const guideline = `G${match[2]}`;
    const score = parseInt(match[3], 10);

    // Clean up the reason by removing trailing/leading quotes, asterisks, markdown, and extra whitespace
    const reason = match[4]
      .trim()
      .replace(/['"`]+/g, "") // Remove quotes
      .replace(/\*+$/g, "") // Remove trailing asterisks
      .replace(/^\*+/g, "") // Remove leading asterisks
      .replace(/\s*\+\s*$/g, "") // Remove trailing + symbols
      .replace(/\s+/g, " ") // Normalize whitespace
      .replace(/\n/g, " ") // Replace line breaks with spaces
      .trim();

    topViolations[`GuidelineViolation${index}`] = guideline;
    topViolations[`ConfidenceScore${index}`] = score;
    topViolations[`Reason${index}`] = reason;
  }

  return topViolations;
}

/**
 * Analyze review violation using AzureOpenAI
 * @param {Object} review - The original review object
 * @param {string} reviewAnalysis - Output from the first prompt
 * @param {string} guidelineViolation - The guideline violation
 * @param {string} reason - The reason given for the violation
 * @returns {Promise<string>}
 */
async function analyzeReviewViolation(
  review,
  reviewAnalysis,
  guidelineViolation,
  reason
) {
  const violationPrompt = `
You are an Amazon review expert. Check carefully if the review violates the given guideline. Think about what the review is really saying and whether it matches the spirit of the guideline — not just the words.

If the review is compliant:
Output only: False Positive

If the review clearly violates the guideline (with more than 85% confidence):
Write a short and clear explanation in simple English (5th grade level). Your output must follow this exact format, write it in a paragraph with not more than 40 words, make sure to adhere to the important rules:

Briefly explain what type of feedback it is (e.g., shipping, price, seller) include reasoning.

Important rules:
- Use actual quotes from part of the review to make your case.
- Do NOT start your output with a quote.
- Instead, start with something like: ["The review focuses on…" or "The reviewer talks about…" or something like that.

Do not guess or add any new info. Be strict and logical. Do not change the format.

End with this exact sentence (no edits, no rewording):
THIS IS A [type of feedback] FEEDBACK NOT PRODUCT REVIEW - WHICH IS NOT ALLOWED AS PER COMMUNITY GUIDELINE
(Replace [type of feedback] with words like "SHIPPING & PACKAGING", "SELLER COMPLAINT", "PRICING ISSUE", etc.)

INPUTS:
Product Title: ${review.productTitle}
Review Title: ${review["Review Title"]}
Review Content: ${review.reviewContent}
Review Context: ${reviewAnalysis}
Guideline violated: ${guidelineViolation}
Potential reason given for violation: ${reason}
`;

  const completion = await client.chat.completions.create({
    messages: [
      {
        role: "system",
        content: "You are an Amazon review violation analysis assistant.",
      },
      {
        role: "user",
        content: violationPrompt,
      },
    ],
    model: process.env.AZURE_OPENAI_MODEL_ID,
    temperature: 0.3,
    max_tokens: 500,
  });

  return completion.choices?.[0]?.message?.content ?? "";
}

/**
 * Analyze Amazon reviews using AzureOpenAI and then check compliance
 * @param {Array} reviewsArray
 * @returns {Promise<Array>}
 */
// async function analyzeAmazonReviewsWithCompliance(reviewsArray) {
//   const analyses = [];

//   for (const [index, review] of reviewsArray.entries()) {
//     // Step 1: Run the first analysis
//     let analysisResult = "";
//     try {
//       analysisResult = await analyzeReviewContent(review);
//     } catch (err) {
//       analysisResult = null;
//     }

//     // Step 2: Run the compliance check if analysis succeeded
//     let complianceResult = "";
//     if (analysisResult) {
//       try {
//         complianceResult = await analyzeReviewCompliance(
//           review,
//           analysisResult
//         );
//       } catch (err) {
//         complianceResult = null;
//       }
//     }

//     // Extract top violations from compliance output
//     const topViolations = extractTopViolations(complianceResult);

//     // Step 3: Run the violation analysis if confidence score is 2 or 3
//     let violationResult = "";
//     if (topViolations.ConfidenceScore1 >= 2) {
//       try {
//         violationResult = await analyzeReviewViolation(
//           review,
//           analysisResult,
//           topViolations.GuidelineViolation1,
//           topViolations.Reason1
//         );
//       } catch (err) {
//         violationResult = null;
//       }
//     }

//     analyses.push({
//       reviewId: review["Review ID"],
//       asin: review.ASIN,
//       productTitle: review.productTitle,
//       productLink: review.productLink,
//       reviewTitle: review["Review Title"],
//       reviewContent: review.reviewContent, // Added this line
//       reviewLink: review["Review URL"],
//       reviewer: review.reviewer,
//       reviewerLink: review.reviewerLink,
//       reviewDate: review.reviewDate,
//       rating: review.reviewScore,
//       analysis: analysisResult,
//       compliance: complianceResult,
//       topViolations: topViolations,
//       violation: violationResult,
//     });
//   }

//   return analyses;
// }

/**
 * Write analysis results to a CSV file
 * @param {Array} results - The analysis results
 */
async function writeToCsv(results) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
  const csvWriter = createCsvWriter({
    path: `output_${timestamp}.csv`,
    header: [
      { id: "asin", title: "ASIN" },
      { id: "reviewId", title: "Review ID" },
      { id: "productLink", title: "Product Link" },
      { id: "reviewLink", title: "Review Link" },
      { id: "reviewContent", title: "Review Content" }, // Added this line
      { id: "prompt1Output", title: "Prompt 1 Output" },
      { id: "prompt2ViolationCode", title: "Prompt 2 Violation Code" },
      { id: "prompt2ConfidenceScore", title: "Prompt 2 Confidence Score" },
      { id: "prompt2Reason", title: "Prompt 2 Reason" },
      { id: "prompt3Output", title: "Prompt 3 Output" },
    ],
  });

  const records = results.map((result) => ({
    asin: result.asin,
    reviewId: result.reviewId,
    productLink: result.productLink,
    reviewLink: result.reviewLink,
    reviewContent: result.reviewContent || "", // Added this line
    prompt1Output: result.analysis || "",
    prompt2ViolationCode: result.topViolations?.GuidelineViolation1 || "",
    prompt2ConfidenceScore: result.topViolations?.ConfidenceScore1 || 0,
    prompt2Reason: result.topViolations?.Reason1 || "",
    prompt3Output: result.violation || "",
  }));

  await csvWriter.writeRecords(records);
  console.log(`✅ CSV results saved to: output.csv_${timestamp}`);
}

// Update the main runner to use the new function

const reviewsArray = [
  {
    ASIN: "B09B1BP1JH",
    "Seller Name": "Belize Belize",
    HelpfulCounts: 10,
    image1: "",
    image2: "",
    image3: "",
    image4: "",
    pageUrl:
      "https://www.amazon.com/product-reviews/B09B1BP1JH/ref=cm_cr_arp_d_viewopt_sr?ie=UTF8&reviewerType=all_reviews&filterByStar=one_star&pageNumber=1",
    "Review ID": "R1I2I0WG0EKGOW",
    productLink: "https://www.amazon.com/dp/B09B1BP1JH",
    productTitle:
      "Spartan Mosquito Pro Tech - 1 Acre Pack 4 Tubes (2 Boxes) 100% American Made",
    reviewContent:
      "EDIT: ABSOLUTE SCAM NO WONDER THEY WERE BEING SUED LEFT RIGHT UP AND DOWN BY THEIR INVESTORS! Yesterday I swatted one mosquito while I was setting these traps up. TODAY HOWEVER, I cannot even stand outside on my property. This drew THE ENTIRE NEIGHBORHOOD OF MOSQUITOS ALL INTO MY YARD. And I have a large yard for livening in town. About half an acre. I followed I structions. Says 80 feet from the house the closest one is 100 feet from my house and it looks like a swarm of flies going after some rotten food around my head. This isn’t right and Amazon needs to take this product off their market I will be talking to them for a refund because I’m not spending 50$ to bring the mosquitos INTO my yard. That’s bull.2 stars not because it doesn’t work I just put them up… but all advertising and videos out there, there are TONS of lawsuits out there had stated 90 days. THE FINE PRINT SAYS UP TO 30 DAYS. So no wonder all the 6-7 figure lawsuits. Can’t say in your advertisement 90 days then put in the fine print up to 30. 50$ every month?! That’s BULL. I will however be buying a big bag of mosquito killer and toss them in there and add water. Consciences of the tubes is the ONLY reason I gave 2 stars because the whole 5 gallon bucket and a 15$ BAG of add to water mosquito killer idea doesn’t work for me as I have a heeler and a border collie and I need to be able to hang these high in trees or they will get to it and although “safe for dogs” is stated on most mostiuto killers that you ad to water would you drink it? I think not. So the convienence of the hanging high up over 5 gallon buckets is the extra start. To the company… don’t say up to 3 months/90 days in all your advertisement videos on line and then put up to 30 on the fine print. You should be sued. A lot. Can’t make this stuf up smdh",
    reviewerCountry: "United States",
    reviewDate: "April 17, 2025",
    reviewScore: 1,
    reviewTitle: "False advertisement. The owner puts videos online. Lies.",
    reviewer: "Brian Mansinon",
    "Review URL": "https://www.amazon.com/gp/customer-reviews/R1I2I0WG0EKGOW",
    reviewerLink:
      "https://www.amazon.com/gp/profile/amzn1.account.AHW53W2YQHOGQZUXSYNBJSSU3R3Q/ref=cm_cr_arp_d_gw_btm?ie=UTF8",
    "": "",
    "#REF!": "",
    isVerified: "",
  },
];

async function writeToJson(results) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
  const filename = `analysis-results-${timestamp}.json`;
  const filepath = path.join(__dirname, filename);

  const jsonData = {
    timestamp: new Date().toISOString(),
    totalReviews: results.length,
    results: results,
  };

  try {
    await fs.writeFile(filepath, JSON.stringify(jsonData, null, 2));
    console.log(`✅ Results saved to: ${filename}`);
  } catch (error) {
    console.error("❌ Error saving JSON file:", error);
  }
}

async function hey() {
  const results = await analyzeAmazonReviewsWithCompliance(reviewsArray);
  console.log("\n📊 Final Results:");
  console.dir(results, { depth: null, colors: true });

  // Write results to JSON with timestamp
  await writeToJson(results);
  // Write results to CSV
  await writeToCsv(results);
}

hey();
module.exports = { analyzeAmazonReviewsWithCompliance };
