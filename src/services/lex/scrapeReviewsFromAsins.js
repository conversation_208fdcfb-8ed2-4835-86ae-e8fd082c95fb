const { getBrowser, closeBrowsers,closePages } = require("../../utils/puppeteer/browserHelper");
const fs = require('fs').promises;
const prisma = require("../../database/prisma/getPrismaClient");

// Add country configuration at the top of the file
const COUNTRY_CONFIG = {
  US: {
    domain: 'amazon.com',
    baseUrl: 'https://www.amazon.com'
  },
  UK: {
    domain: 'amazon.co.uk', 
    baseUrl: 'https://www.amazon.co.uk'
  }
};

// Helper function to get base URL by country code
function getBaseUrlByCountry(countryCode = 'US') {
  const config = COUNTRY_CONFIG[countryCode?.toUpperCase()];
  if (!config) {
    console.warn(`Country code '${countryCode}' not supported, defaulting to US`);
    return COUNTRY_CONFIG.US.baseUrl;
  }
  console.log(`Configgggg: ${config.baseUrl}`)
  return config.baseUrl;
}

// Selectors from your content script
const selectors = {
  allReviews: "#cm_cr-review_list",
  reviewItems: ".review.aok-relative",
  authorName: ".a-profile-name",
  authorNameAlt: ".customerReviewsMobileFeature .a-profile-content span.a-profile-name",
  authorLink: "div[data-hook='genome-widget'] a, .a-profile",
  reviewDate: "span[data-hook='review-date']",
  reviewTitle: ".review-title-content",
  reviewBody: ".review-text-content>span",
  reviewRating: "i[data-hook='review-star-rating']>span, i[data-hook='cmps-review-star-rating']>span, .a-icon-alt",
  verifiedPurchase: "span[data-hook='avp-badge']",
  reviewImg: "img.review-image-tile",
  helpfulCounts: ".cr-vote-text",
  productTitle: ".product-title, #productTitle",
  variantsElement: "a[data-hook='format-strip']",
  nextButton: "li.a-last a",
  disabledNextButton: "li.a-disabled.a-last",
  eachStarReviewCount: "div[data-hook='cr-filter-info-review-rating-count']",
  // Add login form selector
  loginForm: 'form[name="signIn"]'
};

// Simple login detection function
async function checkForLoginForm(page) {
  try {
    const hasLoginForm = await page.evaluate((selector) => {
      return !!document.querySelector(selector);
    }, selectors.loginForm);

    if (hasLoginForm) {
      console.log('🚨 Login form detected - authentication required');
      return true;
    }

    return false;
  } catch (error) {
    console.error('Error checking for login form:', error);
    return false;
  }
}

function extractStarReviewCount(reviewCountText) {
  try {
    if (!reviewCountText || typeof reviewCountText !== 'string') {
      return 0;
    }

    const trimmedText = reviewCountText.trim();
    
    // Check if it starts with "No" - indicates no reviews
    if (trimmedText.toLowerCase().startsWith('no')) {
      return 0;
    }

    const match = trimmedText.match(/^([\d,]+)/);
    if (match && match[1]) {
      // Remove commas and parse to integer
      const numberString = match[1].replace(/,/g, '');
      const number = parseInt(numberString, 10);
      
      if (!isNaN(number) && number >= 0) {
        return number;
      }
    }

    return 0;
  } catch (error) {
    console.error('Error extracting star review count:', error);
    return 0;
  }
}

function removeDuplicateReviews(reviews) {
  const uniqueReviews = new Map();
  
  reviews.forEach(review => {
    const reviewId = review["Review ID"];
    if (reviewId && !uniqueReviews.has(reviewId)) {
      uniqueReviews.set(reviewId, review);
    }
  });
  
  return Array.from(uniqueReviews.values());
}

// Main scraping function
async function scrapeAmazonReviews(asin, cookies, brand = '', sortByMostRecent = false, countryCode = 'US') {
  let browser;
  let page;
  const allReviews = [];
  
  try {
    console.log(`Starting scraper for ASIN: ${asin} (Country: ${countryCode}, sortByMostRecent: ${sortByMostRecent})`);
    
    // Get base URL for the country
    const baseUrl = getBaseUrlByCountry(countryCode);
    console.log(`Using base URL: ${baseUrl}`);
    
    // Use shared browser instance
    browser = await getBrowser();
    page = await browser.newPage();
    
    // Set latest user agent
    await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36');
    
    // Set cookies if provided
    if (cookies && Array.isArray(cookies) && cookies.length > 0) {
      console.log(`Setting ${cookies.length} cookies...`);
      
      // Use cookies directly - they should already have all the required fields
      await page.setCookie(...cookies);
      console.log(`✅ Successfully set ${cookies.length} cookies`);
    } else {
      console.log('No valid cookies provided');
    }

    // Set viewport
    await page.setViewport({ width: 1366, height: 768 });

    // Loop through 1, 2, 3 star ratings
    for (const starRating of [1, 2, 3]) {
      console.log(`\n=== Processing ${starRating}-star reviews ===`);
      
      // First, get the count of reviews for this star rating
      const reviewCount = await getStarReviewCount(page, asin, starRating, baseUrl);
      console.log(`Found ${reviewCount} reviews for ${starRating}-star rating`);
      
      let starReviews = [];
      
      if (reviewCount === 0) {
        console.log(`No reviews found for ${starRating}-star rating, skipping...`);
        continue;
      }
      
      if (reviewCount < 100) {
        // Only scrape "Most Recent" if less than 100 reviews
        if (sortByMostRecent) {
          console.log(`Scraping Most Recent reviews only (${reviewCount} < 100)`);
          const recentReviews = await scrapeStarRating(page, asin, starRating, brand, 'recent', baseUrl);
          starReviews.push(...recentReviews);
        } else {
          console.log(`Scraping Top Reviews only (${reviewCount} < 100)`);
          const topReviews = await scrapeStarRating(page, asin, starRating, brand, 'helpful', baseUrl);
          starReviews.push(...topReviews);
        }
      } else {
        // Scrape both if 100+ reviews and sortByMostRecent is true
        if (sortByMostRecent) {
          console.log(`Scraping both Top Reviews and Most Recent (${reviewCount} >= 100)`);
          
          // Scrape Top Reviews first
          const topReviews = await scrapeStarRating(page, asin, starRating, brand, 'helpful', baseUrl);
          console.log(`Scraped ${topReviews.length} Top Reviews`);
          
          // Wait between different sort types
          await new Promise(resolve => setTimeout(resolve, 3000));
          
          // Scrape Most Recent
          const recentReviews = await scrapeStarRating(page, asin, starRating, brand, 'recent', baseUrl);
          console.log(`Scraped ${recentReviews.length} Most Recent Reviews`);
          
          // Combine and remove duplicates
          const combinedReviews = [...topReviews, ...recentReviews];
          const uniqueReviews = removeDuplicateReviews(combinedReviews);
          
          console.log(`After removing duplicates: ${uniqueReviews.length} unique reviews`);
          starReviews = uniqueReviews;
        } else {
          console.log(`Scraping Top Reviews only (sortByMostRecent disabled)`);
          const topReviews = await scrapeStarRating(page, asin, starRating, brand, 'helpful', baseUrl);
          starReviews = topReviews;
        }
      }
      
      allReviews.push(...starReviews);
      console.log(`Total collected for ${starRating}-star: ${starReviews.length} reviews`);
      
      // Wait between star ratings to avoid being blocked
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    console.log(`\nTotal reviews collected: ${allReviews.length}`);
    
    // Final deduplication across all star ratings
    const finalUniqueReviews = removeDuplicateReviews(allReviews);
    console.log(`Final unique reviews after global deduplication: ${finalUniqueReviews.length}`);
    
    return {
      success: true,
      totalReviews: finalUniqueReviews.length,
      reviews: finalUniqueReviews
    };

  } catch (error) {
    console.error('Error in scrapeAmazonReviews:', error);
    return {
      success: false,
      error: error.message,
      reviews: allReviews
    };
  } finally {
    if (browser) {
      if (page) {
        try {
          await page.close();
        } catch (err) {
          console.error('Error closing page:', err);
        }
      }
    }
  }
}

async function getStarReviewCount(page, asin, starRating, baseUrl) {
  try {
    const filterParam = getStarFilterParam(starRating);
    const reviewsUrl = `${baseUrl}/product-reviews/${asin}?filterByStar=${filterParam}&pageNumber=1`;
    
    console.log(`Getting review count from: ${reviewsUrl}`);
    await page.goto(reviewsUrl, { waitUntil: 'networkidle2', timeout: 30000 });
      
    // Check for login form ONLY here - this is our early detection point
    const hasLoginForm = await checkForLoginForm(page);
    if (hasLoginForm) {
      throw new Error('LOGIN_FORM_DETECTED: Login form detected - cookies expired');
    }
    
    // Wait for the review count element
    await page.waitForSelector(selectors.eachStarReviewCount, { timeout: 10000 });
    
    // Check if element has text content
    const hasText = await page.evaluate((selector) => {
      const element = document.querySelector(selector);
      return element && element.textContent.trim().length > 0;
    }, selectors.eachStarReviewCount);
    
    if (!hasText) {
      console.log('Review count element has no text, reloading page...');
      await page.reload({ waitUntil: 'networkidle2', timeout: 30000 });
      await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds
      
      // Try again after reload
      await page.waitForSelector(selectors.eachStarReviewCount, { timeout: 10000 });
    }
    
    // Extract review count text
    const reviewCountText = await page.evaluate((selector) => {
      const element = document.querySelector(selector);
      return element ? element.textContent.trim() : '';
    }, selectors.eachStarReviewCount);
    
    console.log(`Review count text: "${reviewCountText}"`);
    
    return extractStarReviewCount(reviewCountText);
  } catch (error) {
    if (error.message.includes('LOGIN_FORM_DETECTED')) {
      throw error;
    }
    console.error(`Error getting review count for ${starRating}-star:`, error);
    return 0;
  }
}

async function scrapeStarRating(page, asin, starRating, brand, sortBy = 'helpful', baseUrl) {
  const reviews = [];
  let hasNextPage = true;
  let pageCount = 0;
  
  try {
    // Navigate to reviews page with star filter and sort option
    const filterParam = getStarFilterParam(starRating);
    const reviewsUrl = `${baseUrl}/product-reviews/${asin}?ie=UTF8&reviewerType=all_reviews&sortBy=${sortBy}&filterByStar=${filterParam}&pageNumber=1`;
    
    console.log(`Navigating to: ${reviewsUrl}`);
    await page.goto(reviewsUrl, { waitUntil: 'networkidle2', timeout: 30000 });
    
    while (hasNextPage && pageCount < 50) {
      pageCount++;
      console.log(`Scraping page ${pageCount} for ${starRating}-star reviews (${sortBy})`);
      
      // Wait for reviews container
      await page.waitForSelector(selectors.allReviews, { timeout: 10000 });
      
      // Check if reviews container has text content
      let hasReviewText = await page.evaluate((selector) => {
        const reviewsContainer = document.querySelector(selector);
        return reviewsContainer && reviewsContainer.textContent.trim().length > 0;
      }, selectors.allReviews);
      
      if (!hasReviewText) {
        console.log('❌ No review text found - this indicates cookies have expired');
        // Don't reload, throw error immediately
        throw new Error('LOGIN_FORM_DETECTED: Chrome reload page error - cookies expired. Reviews container is empty indicating authentication failure.');
      }
      
      // Extract reviews from current page
      const pageReviews = await page.evaluate((selectors, asin, brand, sortBy, baseUrl) => {
        
        // Helper functions in page context
        const extractText = (element, selector) => {
          const el = element.querySelector(selector);
          return el ? el.textContent.trim() : '';
        };

        const extractTitleText = (parentElement, targetSelector) => {
          const targetElement = parentElement.querySelector(targetSelector);
          if (!targetElement) return '';

          const directChildSpans = targetElement.querySelectorAll(':scope > span');
          return Array.from(directChildSpans)
            .map(span => (span.textContent || '').trim())
            .filter(text => text.length > 0)
            .join(' ');
        };

        const elementExists = (element, selector) => {
          return element.querySelector(selector) !== null;
        };

        const extractHelpfulCounts = (element) => {
          try {
            const helpfulText = extractText(element, selectors.helpfulCounts);
            if (!helpfulText || helpfulText.trim() === '') return 0;
            
            if (helpfulText.startsWith('One person') || helpfulText === 'One') return 1;
            
            const match = helpfulText.match(/(\d+)/);
            return match ? parseInt(match[1]) : 0;
          } catch (error) {
            return 0;
          }
        };

        const getImgArray = (element) => {
          try {
            const images = [];
            const imgElements = element.querySelectorAll(selectors.reviewImg);
            
            for (let i = 0; i < imgElements.length && i < 4; i++) {
              const imageUrl = imgElements[i].getAttribute('src') || '';
              if (imageUrl) images.push(imageUrl);
            }

            while (images.length < 4) {
              images.push('');
            }

            return images;
          } catch (error) {
            return ['', '', '', ''];
          }
        };

        const extractCountryAndDate = (dateString) => {
          try {
            const regex = /Reviewed in (?:the )?(.*?) on (.*)/i;
            const match = dateString.match(regex);
            
            if (match && match.length === 3) {
              return {
                reviewerCountry: match[1].trim(),
                reviewDate: match[2].trim()
              };
            }
            
            const altRegex = /Reviewed on (.*)/i;
            const altMatch = dateString.match(altRegex);
            
            if (altMatch && altMatch.length === 2) {
              return {
                reviewerCountry: "",
                reviewDate: altMatch[1].trim()
              };
            }
            
            return {
              reviewerCountry: "",
              reviewDate: dateString
            };
          } catch (error) {
            return {
              reviewerCountry: "",
              reviewDate: dateString
            };
          }
        };

        const segregateVariantText = (parentElement, selector) => {
          if (!parentElement || typeof parentElement.querySelector !== 'function') {
            return {};
          }

          const anchorTag = parentElement.querySelector(selector);
          if (!anchorTag || anchorTag.tagName.toLowerCase() !== 'a') {
            return {};
          }

          const variants = {};
          let currentIndex = 0;
          let currentTextAccumulator = "";

          for (const node of anchorTag.childNodes) {
            if (node.nodeType === 3) { // TEXT_NODE
              currentTextAccumulator += node.textContent.trim();
            } else if (node.nodeType === 1 && // ELEMENT_NODE
                       node.tagName === 'I' && 
                       node.classList.contains('a-icon-text-separator')) {
              if (currentTextAccumulator) {
                variants[`variant${currentIndex}`] = currentTextAccumulator;
                currentIndex++;
                currentTextAccumulator = "";
              }
            }
          }

          if (currentTextAccumulator) {
            variants[`variant${currentIndex}`] = currentTextAccumulator;
          }

          return variants;
        };

        const extractReviewerID = (reviewerLink) => {
          try {
            if (!reviewerLink) return null;
            
            // Amazon reviewer profile URLs contain the reviewer ID
            // Format: https://www.amazon.com/gp/profile/amzn1.account.XXXXX/ref=...
            const match = reviewerLink.match(/\/profile\/(amzn1\.account\.[A-Z0-9]+)/i);
            return match ? match[1] : null;
          } catch (error) {
            return null;
          }
        };

        // Main extraction logic
        const reviewItems = document.querySelectorAll(selectors.reviewItems);
        const reviews = [];
        
        // Debug: Log how many review items found
        console.log(`Found ${reviewItems.length} review items on page`);
        
        // Get product title
        const productTitleElement = document.querySelector(selectors.productTitle);
        const productTitle = productTitleElement ? productTitleElement.textContent.trim() : '';
        
        reviewItems.forEach((review, index) => {
          try {
            // FIXED: Better Review ID extraction
            let reviewerId = review.id || '';
            if (!reviewerId) {
              // Try data-hook attribute
              reviewerId = review.getAttribute('data-hook') || '';
            }
            
            // Skip if no review ID found
            if (!reviewerId) {
              console.log(`Skipping review ${index} - no ID found`);
              return;
            }
            
            // Extract rating
            const ratingText = extractText(review, selectors.reviewRating);
            let rating = 0;
            if (ratingText) {
              const ratingMatch = ratingText.match(/(\d+(\.\d+)?)/);
              rating = ratingMatch ? parseFloat(ratingMatch[1]) : 0;
            }
            
            const description = extractText(review, selectors.reviewBody);
            const title = extractTitleText(review, selectors.reviewTitle);
            const reviewLink = `${baseUrl}/gp/customer-reviews/${reviewerId}`;
            const dateText = extractText(review, selectors.reviewDate);
            const { reviewerCountry, reviewDate } = extractCountryAndDate(dateText);
            
            const authorElem = review.querySelector(selectors.authorName);
            const authorAltElem = review.querySelector(selectors.authorNameAlt);
            const username = authorElem ? authorElem.textContent.trim() : 
                            (authorAltElem ? authorAltElem.textContent.trim() : '');
            
            const authorLinkElem = review.querySelector(selectors.authorLink);
            const userLinkHref = authorLinkElem ? authorLinkElem.getAttribute('href') : '';
            const userLink = userLinkHref ? (userLinkHref.startsWith('http') ? userLinkHref : `https://www.amazon.com${userLinkHref}`) : '';

            // Extract reviewer ID from the profile link
            const reviewerID = extractReviewerID(userLink);
            
            const isVerified = elementExists(review, selectors.verifiedPurchase) ? 'True' : 'False';
            const HelpfulCounts = extractHelpfulCounts(review);
            const images = getImgArray(review);
            const productLink = `${baseUrl}/dp/${asin}`;
            const pageUrl = window.location.href;
        
            const variants = segregateVariantText(review, selectors.variantsElement);

            const reviewData = {
              ASIN: asin,
              "Seller Name": brand,
              HelpfulCounts: HelpfulCounts,
              image1: images[0],
              image2: images[1],
              image3: images[2],
              image4: images[3],
              pageUrl,
              "Review ID": reviewerId,
              productLink,
              productTitle,
              reviewContent: description,
              reviewerCountry,
              reviewDate: reviewDate,
              reviewScore: rating,
              "Review Title": title,
              reviewer: username,
              "Review URL": reviewLink,
              reviewerLink: userLink,
              reviewerID: reviewerID,
              variant_0: variants.variant0 || '',
              variant_1: variants.variant1 || '',
              isVerified,
            };
            
            reviews.push(reviewData);
          } catch (error) {
            console.error(`Error extracting review ${index}:`, error);
          }
        });
        
        return reviews;
      }, selectors, asin, brand, sortBy, baseUrl);
      
      reviews.push(...pageReviews);
      console.log(`Extracted ${pageReviews.length} reviews from page ${pageCount} (${sortBy})`);
      
      // Check for next page and click it
      hasNextPage = await page.evaluate((selectors) => {
        const nextButton = document.querySelector(selectors.nextButton);
        const disabledNextButton = document.querySelector(selectors.disabledNextButton);
        
        if (nextButton && !disabledNextButton) {
          nextButton.click();
          return true;
        }
        return false;
      }, selectors);
      
      if (hasNextPage) {
        // Wait for next page to load
        await new Promise(resolve => setTimeout(resolve, 3000));
      }
    }
    
  } catch (error) {
    // Check if it's a cookie expiration error
    if (error.message.includes('LOGIN_FORM_DETECTED') || error.message.includes('cookies expired')) {
      console.error(`🔐 Authentication Cookies Error: ${error.message}`);
      throw error; // Re-throw to be handled by worker
    }
    
    console.error(`Error scraping ${starRating}-star reviews (${sortBy}):`, error);
    throw error;
  }
  
  return reviews;
}

function getStarFilterParam(starRating) {
  switch (starRating) {
    case 1: return 'one_star';
    case 2: return 'two_star';
    case 3: return 'three_star';
    case 4: return 'four_star';
    case 5: return 'five_star';
    default: return 'one_star';
  }
}

async function saveToCSV(reviews, asin) {
  try {
    if (reviews.length === 0) {
      console.log('No reviews to save');
      return;
    }
    
    const headers = Object.keys(reviews[0]);
    const csvContent = [
      headers.join(','),
      ...reviews.map(review => 
        headers.map(header => {
          const value = review[header] || '';
          // Escape quotes and wrap in quotes if contains comma
          const escapedValue = String(value).replace(/"/g, '""');
          return escapedValue.includes(',') ? `"${escapedValue}"` : escapedValue;
        }).join(',')
      )
    ].join('\n');
    
    const filename = `amazon_reviews_${asin}_${Date.now()}.csv`;
    await fs.writeFile(filename, csvContent);
    console.log(`Reviews saved to ${filename}`);
  } catch (error) {
    console.error('Error saving CSV:', error);
  }
}

// Update the getActiveCookiesFromDB function
async function getActiveCookiesFromDB() {
  try {
    const activeCookies = await prisma.lexReviewScraperCookies.findMany({
      where: {
        cookieStatus: "ACTIVE",
        active: true
      },
      orderBy: {
        updatedAt: "desc"
      }
    });

    if (!activeCookies || activeCookies.length === 0) {
      console.log('No active cookies found in database');
      return [];
    }

    // Parse the first available cookie JSON array
    const cookieJsonString = activeCookies[0].cookieKey;
    console.log(`Using cookies from email: ${activeCookies[0].emailId}`);
    
    try {
      const cookieArray = JSON.parse(cookieJsonString);
      
      // Ensure it's an array and has valid cookies
      if (Array.isArray(cookieArray) && cookieArray.length > 0) {
        console.log(`Loaded ${cookieArray.length} cookies from database`);
        return cookieArray;
      } else {
        console.log('Cookie array is empty or invalid');
        return [];
      }
    } catch (parseError) {
      console.error('Error parsing cookie JSON from database:', parseError);
      return [];
    }
  } catch (error) {
    console.error('Error fetching cookies from database:', error);
    return [];
  }
}

// Export the main function
module.exports = {
  scrapeAmazonReviews,
  getActiveCookiesFromDB,
  COUNTRY_CONFIG,
  getBaseUrlByCountry
};

// Example usage
async function runExampleScraper() {
  try {
    console.log('Getting cookies from database...');
    const cookies = await getActiveCookiesFromDB();
    
    const sortByMostRecent = true;
    const countryCode = 'UK'; // or 'UK'
    
    // Run the scraper with country code
    const result = await scrapeAmazonReviews('B0DS2HVX4K', cookies, 'Your Brand Name', sortByMostRecent, countryCode);
    console.log('Scraping completed:', result);
    
  } catch (error) {
    console.error('Scraping failed:', error);
  }
}

// runExampleScraper();

  //<form name="signIn" method="post" novalidate="" action="https://www.amazon.com/ap/signin" class="auth-validate-form auth-real-time-validation a-spacing-none" data-fwcim-id="6ZsKAvtG">
    //<input type = "password">