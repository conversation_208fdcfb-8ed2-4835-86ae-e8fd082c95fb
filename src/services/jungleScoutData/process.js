const axios = require("axios");
const { sendErrorEmail } = require("../../utils/mailHelper");
const { getJungleKeys } = require("../../models/configuration");
require("dotenv").config();
const Sentry = require("@sentry/node");
const { jungleScoutPricingCalculator } = require("../../utils/pricing/pricingCalculator");
const getSalesEstimate = async (asin, clientId) => {
  try {
    const marketplace = "us";
    let end_date = new Date();
    end_date.setDate(end_date.getDate() - 1); // set to yesterday

    let start_date = new Date();
    start_date.setDate(start_date.getDate() - 30); // set to 30 days ago
    start_date = start_date.toISOString().split("T")[0];
    end_date = end_date.toISOString().split("T")[0];
    console.log(
      "Getting sales estimate from jungle scout for asin:-----------",
      asin,
      "from",
      start_date,
      "to",
      end_date,
      "in",
      marketplace
    );
    // Call Jungle Scout API
    const { apiKey, keyName, baseURL } = await getJungleKeys(clientId);
    // const apiKey = process.env.JUNGLE_SCOUT_API_KEY;
    // const keyName = process.env.JUNGLE_SCOUT_KEY_NAME;
    // const baseURL = "https://developer.junglescout.com";
    const response = await axios.get(`${baseURL}/api/sales_estimates_query`, {
      headers: {
        Authorization: `${keyName}:${apiKey}`,
        "X-API-Type": "junglescout",
        Accept: "application/vnd.junglescout.v1+json",
        "Content-Type": "application/vnd.api+json",
      },
      params: {
        marketplace: marketplace,
        asin: asin,
        start_date: start_date,
        end_date: end_date,
      },
    });
    let data = response.data?.data?.[0]?.attributes?.data;
    // console.log(data);
    jungleScoutPricingCalculator(asin);
    if (!data === null || data === undefined || data.length === 0) {
      console.error("No data found in jungle scout response");
      return {
        Units: 0,
        revenue: 0,
        status: "No data found in jungle scout response",
      };
    }
    let sales = data.reduce(
      (acc, curr) => {
        acc.Units += curr.estimated_units_sold;
        acc.revenue += curr.estimated_units_sold * curr.last_known_price;
        return acc;
      },
      { Units: 0, revenue: 0, status: "Jungle Scout" }
    );

    return sales;
  } catch (error) {
    if (error.response.status == 422) {
      console.log("No Data From Jungle Scout For ASIN:", asin);
      return {
        Units: 0,
        revenue: 0,
        status: `Failed fetching jungle scout data API ERROR: ${error.response.data?.errors?.[0]?.detail}`,
      };
    }
    console.log(error.response.status);
    console.error("Error Stack in JungleScout:", error.stack);
    //  TODO: Specify Credit Error !!
    Sentry.captureException("Error fetching jungle scout data:", error);
    if (
      error.response.data?.errors?.[0]?.code !== "UNSUPPORTED_CATEGORY_VALUE"
    ) {
      // sendErrorEmail("jungleScoutApiError");
    }
    console.error("Error in JungleScout:", error.response.data);

    return {
      Units: 0,
      revenue: 0,
      status: `Failed fetching jungle scout data API ERROR: ${error.response.data?.errors?.[0]?.detail}`,
    };
  }
};

const getKeywordsForAsins = async (
  asins,
  marketplace = "us",
  sort = "-monthly_search_volume_exact",
  pageSize = 50,
  pageCursor = ""
) => {
  try {
    const { apiKey, keyName, baseURL } = await getJungleKeys(clientId);
    const apiUrl =
      "https://developer.junglescout.com/api/keywords/keywords_by_asin_query";
    const headers = {
      X_API_Type: "junglescout",
      Accept: "application/vnd.junglescout.v1+json",
      "Content-Type": "application/vnd.api+json",
      Authorization: `${keyName}:${apiKey}`,
    };

    const data = {
      data: {
        type: "keywords_by_asin_query",
        attributes: {
          asins: asins,
          include_variants: true,
          min_monthly_search_volume_exact: 1,
          max_monthly_search_volume_exact: 99999,
          min_monthly_search_volume_broad: 1,
          max_monthly_search_volume_broad: 99999,
          min_word_count: 1,
          max_word_count: 99999,
          min_organic_product_count: 1,
          max_organic_product_count: 99999,
        },
      },
    };

    const params = {
      marketplace,
      sort,
      "page[size]": pageSize,
      "page[cursor]": pageCursor,
    };

    const response = await axios.post(apiUrl, data, { headers, params });
    // From response.data get the keywords
    const keywords = response.data.data.map((keyword) => {
      return {
        keyword: keyword.id,
      };
    });
    return keywords;
  } catch (error) {
    console.error("Error fetching keywords:", error);
    throw error;
  }
};

const exampleUsage = async () => {
  const asins = "B076GXFWCZ";
  try {
    const data = await getSalesEstimate(asins, 1);
    // console.log(data);
  } catch (error) {
    console.error(error);
  }
};
// exampleUsage();
module.exports = { getSalesEstimate, getKeywordsForAsins };
