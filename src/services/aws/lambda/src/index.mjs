// import puppeteer from "puppeteer-extra";
import puppeteer from "puppeteer";
import StealthPlugin from "puppeteer-extra-plugin-stealth";
import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import axios from "axios";
import chromium from "@sparticuz/chromium";

// puppeteer.use(StealthPlugin());

const bucketName = "eq--assets";
const imageFolder = "images/";
const pdfFolder = "pdfs/";

const s3Client = new S3Client({
  region: "ap-south-1",
  credentials: {
    accessKeyId: "********************",
    secretAccessKey: "4GXCp18ebU1YWqL6VKJVuv3Ahxondhl2fbi68w2q",
  },
});

async function uploadImage(imageBuffer, imageName) {
  const params = {
    Bucket: bucketName,
    Key: `${imageFolder}${imageName}`,
    Body: imageBuffer,
    ContentType: "image/jpeg", // or the appropriate image MIME type
  };

  try {
    const s3Response = await s3Client.send(new PutObjectCommand(params));
    console.log(imageName + " uploaded successfully:", s3Response);
  } catch (error) {
    console.error("Error uploading image or sending :", error);
  }
}

async function uploadPDF(pdfBuffer, pdfName) {
  const params = {
    Bucket: bucketName,
    Key: `${pdfFolder}${pdfName}`,
    Body: pdfBuffer,
    ContentType: "application/pdf",
  };

  try {
    const s3Response = await s3Client.send(new PutObjectCommand(params));
    console.log("PDF uploaded successfully:", s3Response);
  } catch (error) {
    console.error("Error uploading PDF or sending:", error);
  }
}

async function generatePDF(url, companyName) {
  console.log("Generating PDF for company:", companyName);
  const browser = await puppeteer.launch({
    headless: true,
    args: ["--no-sandbox", "--disable-setuid-sandbox"],
    timeout: 60000, // 60 seconds
  });
  try {
    const page = await browser.newPage();
    await page.goto(url, { waitUntil: "networkidle2" });

    const pdfBuffer = await page.pdf({
      format: "A4",
      printBackground: true,
      preferCSSPageSize: true,
      margin: { top: "1cm", right: "1cm", bottom: "1cm", left: "1cm" },
      scale: 0.6,
      landscape: false,
    });

    await uploadPDF(pdfBuffer, `${companyName}.pdf`);
    console.log("PDF generated and uploaded successfully");
  } catch (error) {
    console.error(
      `Error occurred while taking pdf of company- ${companyName} : `,
      error.message
    );
  } finally {
    await browser.close();
  }
}

async function generateImages(url, slug) {
  try {
    console.log("Capturing product for:", slug);
    chromium.setHeadlessMode = true;

    // Optional: If you'd like to disable webgl, true is the default.
    chromium.setGraphicsMode = false;

    // Optional: Load any fonts you need. Open Sans is included by default in AWS Lambda instances
    await chromium.font(
      "https://raw.githack.com/googlei18n/noto-emoji/master/fonts/NotoColorEmoji.ttf"
    );
    const browser = await puppeteer.launch({
      args: chromium.args,
      defaultViewport: chromium.defaultViewport,
      executablePath: await chromium.executablePath(),
      headless: chromium.headless,
    });
    // const browser = await puppeteer.launch({});

    const page = await browser.newPage();

    // Use ScraperAPI to handle the request
    const scraperApiUrl = `http://api.scraperapi.com?api_key=${"********************************"}&url=${encodeURIComponent(
      url
    )}`;
    const response = await axios.get(scraperApiUrl);
    const htmlContent = response.data;

    await page.setContent(htmlContent, { waitUntil: "networkidle2" });
    const acceptBtnSelector = "#sp-cc-accept";
    const element = await page.$(acceptBtnSelector);
    if (element) {
      console.log("Cookie Accept Button Pressed.");
      await page.click(acceptBtnSelector);
    } else {
      console.log("No Cookie Accept Button.");
    }
    await page.setViewport({
      width: 1920,
      height: 1080,
      deviceScaleFactor: 2,
    });

    const productImage = await page.$(".imageBlockRearch");
    if (productImage) {
      const box = await productImage.boundingBox();

      const productImageBuffer = await page.screenshot({
        encoding: "binary",
        clip: {
          x: box.x,
          y: box.y,
          width: box.width + 20,
          height: box.height + 20,
        },
      });
      uploadImage(productImageBuffer, [`${slug}_product_image.png`]);
    } else {
      console.log("Product image not found");
    }

    const wholePageBuffer = await page.screenshot({
      encoding: "binary",
    });

    await uploadImage(wholePageBuffer, `${slug}_page_image.png`);

    await browser.close();

    console.log("Screenshots captured and uploaded successfully");
  } catch (error) {
    console.error("Error occurred while capturing product:", error.message);
  }
}

export const handler = async (event) => {
  const { action, url, identifier } = event;

  if (action === "generatePDF") {
    await generatePDF(url, identifier);
  } else if (action === "generateImages") {
    await generateImages(url, identifier);
  } else {
    throw new Error("Invalid action specified");
  }

  return {
    statusCode: 200,
    body: JSON.stringify({ message: "Operation completed successfully" }),
  };
};

// handler({
//   action: "generateImages",
//   url: "https://www.amazon.co.uk/Enhanced-Pomegranate-Ingredients-Free-Soul/dp/B08WPFV3VC/ref=sr_1_1?dib=eyJ2IjoiMSJ9.W-lL_mvp_m2sBQRPvshc_ntqVqUYQW3XQaqFvjDylyoLJG2Y535zJ5IzqdNaNhDWKT14ZyzqCoksXrRk0f00lAAejwFTUWbthPdn5UO2JDPBlUOvgs1HnhWvsyiduiFibAh7X4l_NQZTGbPWzHsr5yxXMBkPw43_E5pG92M2oJB3UbMFRiwc9pkTCA-ct11Wd3MH6L921o-nDhzsWOOQMpON-EHsL46h3LRQON2Yr6M.lE8rrb7j8l5xUnJxXxRNkMsSfMr2IIwDX_s5YRauvm0&dib_tag=se&m=A1SFIMCH4IZ3GA&qid=1727092532&s=merchant-items&sr=1-1",
//   identifier: "ZLINE",
// });
