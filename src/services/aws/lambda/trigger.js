const { LambdaClient, InvokeCommand } = require("@aws-sdk/client-lambda");
require("dotenv").config();

const lambdaClient = new LambdaClient({
  region: "ap-south-1",
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  },
});

async function triggerLambda(action, url, identifier) {
  const params = {
    FunctionName: "eqImageServer",
    Payload: JSON.stringify({
      action: action,
      url: url,
      identifier: identifier,
    }),
  };

  try {
    const command = new InvokeCommand(params);
    const response = await lambdaClient.send(command);
    console.log("Lambda response:", JSON.parse(new TextDecoder("utf-8").decode(response.Payload)));
  return response;
  } catch (error) {
    console.error("Error invoking Lambda function:", error);
  }
}

module.exports = triggerLambda;
// Example usage:
// triggerLambda("generateImages", "https://www.amazon.com/Calendar-Disc-Bound-Planners-Levenger-Notebook/dp/B06XY4VDXR/ref=sr_1_1?m=AMKIKL8YPWG08&marketplaceID=ATVPDKIKX0DER&s=merchant-items&sr=1-1B06XY4VDXR", "BOSCH1");
// triggerLambda("generatePDF", "https://www.amazon.com/Calendar-Disc-Bound-Planners-Levenger-Notebook/dp/B06XY4VDXR/ref=sr_1_1?m=AMKIKL8YPWG08&marketplaceID=ATVPDKIKX0DER&s=merchant-items&sr=1-1B06XY4VDXR", "BOSCH1");
