const {
  S3Client,
  PutObjectCommand,
  HeadObjectCommand,
  DeleteObjectCommand,
} = require("@aws-sdk/client-s3");
const { fromEnv } = require("@aws-sdk/credential-provider-env");
const fs = require("fs");
const path = require("path");
require("dotenv").config();

const bucketName = "eq--assets";
const imageFolder = "images/";
const pdfFolder = "pdfs/";
const jeffResultsFolder = "jeff-results/";

const s3Client = new S3Client({
  region: process.env.AWS_REGION,
  credentials: fromEnv(),
  signatureVersion: "v4",
});

function getS3Url(imageName) {
  return `https://${bucketName}.s3.ap-south-1.amazonaws.com/${imageFolder}${imageName}`;
}

async function uploadImage(imageBuffer, imageName, folder = imageFolder,bucket=bucketName) {
  const params = {
    Bucket: bucket,
    Key: `${folder}${imageName}`,
    Body: imageBuffer,
    ContentType: "image/jpeg", // or the appropriate image MIME type
  };

  try {
    const s3Response = await s3Client.send(new PutObjectCommand(params));
    console.log(imageName + " uploaded successfully:");
    return s3Response;
  } catch (error) {
    console.error("Error uploading image or sending:", error);
  }
}

async function deleteImage(imageName, folder) {
  const params = {
    Bucket: bucketName,
    Key: `${folder}${imageName}`,
  };

  try {
    const s3Response = await s3Client.send(new DeleteObjectCommand(params));
    console.log(imageName + " deleted successfully:", s3Response);
    return s3Response;
  } catch (error) {
    console.error("Error deleting image:", error);
  }
}

async function uploadPDF(pdfBuffer, pdfName, folder = pdfFolder) {
  const params = {
    Bucket: bucketName,
    Key: `${folder}${pdfName}`,
    Body: pdfBuffer,
    ContentType: "application/pdf",
  };

  try {
    const s3Response = await s3Client.send(new PutObjectCommand(params));
    console.log("PDF uploaded successfully:", s3Response);
    return s3Response;
  } catch (error) {
    console.error("Error uploading PDF or sending:", error);
  }
}

async function isValidS3Link(url) {
  const { Bucket, Key } = parseS3Url(url);

  try {
    await s3Client.send(new HeadObjectCommand({ Bucket, Key }));
    return true;
  } catch (error) {
    if (error.name === "NotFound") {
      return false;
    }
    throw error;
  }
}

function parseS3Url(url) {
  const match = url.match(
    /^https:\/\/([^\.]+)\.s3\.([^\.]+)\.amazonaws\.com\/(.+)$/
  );
  if (!match) {
    throw new Error("Invalid S3 URL");
  }
  return {
    Bucket: match[1],
    Key: match[3],
  };
}

async function uploadCsv(csvBuffer, csvName, folder = jeffResultsFolder) {
  const params = {
    Bucket: bucketName,
    Key: `${folder}${csvName}`,
    Body: csvBuffer,
    ContentType: "text/csv",
  };
  try {
    const s3Response = await s3Client.send(new PutObjectCommand(params));
    s3Response.url = getS3Url(csvName, folder);
    console.log("CSV uploaded successfully:", s3Response);
    return s3Response;
  } catch (error) {
    console.error("Error uploading CSV or sending:", error);
  }
}

async function exampleUsage() {
  try {
    // const imageBuffer = fs.readFileSync(
    //   "./scrapeAmazon/screenshotHelper/BOSCH-1722440953074-product_image.png"
    // );
    // const pdfBuffer = fs.readFileSync(
    //   "./scrapeAmazon/screenshotHelper/sample-1722448435692.pdf"
    // );
    const csvBuffer = fs.readFileSync(
      "./CRE_Titles_with_Tiers.csv"
    );

    // await uploadImage(imageBuffer, "BOSCH-1722440953074-product_image.png");
    // await uploadPDF(pdfBuffer, "sample-1722448435692.pdf");
    await uploadCsv(csvBuffer, "CRE_Titles_with_Tiers.csv");
  } catch (error) {
    console.error("Error reading files or uploading:", error);
  }
}
function getS3Url(imageName, folder = imageFolder) {
  return `https://${bucketName}.s3.ap-south-1.amazonaws.com/${folder}${imageName}`;
}

// Example usage
// const s3Link = 'https://eq--assets.s3.ap-south-1.amazonaws.com/images/my-technology-geeks-b0bt8sms1f_page.png';

// isValidS3Link(s3Link).then(isValid => {
//   console.log(`The S3 link is ${isValid ? 'valid' : 'invalid'}.`);
// }).catch(error => {
//   console.error('Error checking S3 link:', error);
// });

// Call exampleUsage to test the functions
if (require.main === module) {
  exampleUsage();
}

module.exports = {
  uploadImage,
  uploadPDF,
  isValidS3Link,
  getS3Url,
  deleteImage,
  getS3Url,
  uploadCsv
};
