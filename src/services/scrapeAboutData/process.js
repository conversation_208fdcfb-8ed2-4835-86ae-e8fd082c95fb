const { JSDOM } = require("jsdom");
const fetch = require("node-fetch");
const { Readability } = require("@mozilla/readability");
const prisma = require("../../database/prisma/getPrismaClient");
const Sentry = require("@sentry/node");

const MAX_RETRIES = 3;
const RETRY_DELAY = 5000;

const excludeKeywords = [
  "/sitemap",
  "/blog",
  "gallery",
  "/success-story",
  "contact",
  "/products",
  "/posts",
  "/videos",
  "/case-studies",
  "/news/",
  "/partners/",
  "/services/",
  "/portfolio/",
  "/media"
];
const aboutKeywords = [
  "about",
  "story",
  "-mission",
  "/mission",
  "-vision",
  "/vision",
  "goal",
  "our-company",
];
const sitemapTemplates = [
  "sitemap.xml",
  "sitemap_index.xml",
  "sitemap/sitemap.xml",
  "sitemap/index.xml",
  "sitemap1.xml",
  "wp-sitemap.xml",
];
const xmlParser = require("xml-js");
async function sendRequest(url, retries = 0) {
  try {
    if (retries > MAX_RETRIES) {
      throw new Error(`Max Retries Exceeded for ${url}`);
    }
    const response = await fetch(url, { timeout: 30000 });
    // catch the redirected url
    // if (response.code === 301 || response.code === 302) {
    //   console.log("Redirected to:", response);
    //   console.log("Redirected to:", response.headers.location);
    //   return sendRequest(response.headers.location, retries + 1);
    // }
    return response;
  } catch (error) {
    console.error("Error Stack:", error.stack);
    Sentry.captureException("Error in sendRequest:", error);
    if (
      url.startsWith("https") &&
      error.name === "FetchError" &&
      error.code === "DEPTH_ZERO_SELF_SIGNED_CERT"
    ) {
      return sendRequest(url.replace("https", "http"), retries + 1);
    }
    throw error;
  }
}

function cleanUrl(myUrl) {
  //   console.log("myUrl", myUrl);
  if (!myUrl.startsWith("http")) {
    myUrl = "https://" + myUrl;
  }
  const url = new URL(myUrl);
  let netloc = url.hostname || url.pathname;
  let path = url.pathname;
  if (!netloc.startsWith("www.")) {
    netloc = "www." + netloc;
  }
  return `https://${netloc}${path}`;
}

async function getAboutText(url) {
  try {
    const response = await sendRequest(url);
    const html = await response.text();
    const doc = new JSDOM(html, { url });
    const reader = new Readability(doc.window.document);
    const article = reader.parse();
    return article.textContent.replace(/\s+/g, " ");
  } catch (error) {
    console.error("Error Stack:", error.stack);
    console.error(`Failed to get about text from ${url}: ${error}`);
    return "";
  }
}

function validAboutUrl(url) {
  return (
    aboutKeywords.some((keyword) => url.includes(keyword)) &&
    !excludeKeywords.some((keyword) => url.includes(keyword))
  );
}

function validSitemapUrl(url) {
  return url.includes("sitemap");
}

async function fetchSitemapUrls(url) {
  console.log("Fetching Sitemap Urls:", url);
  const finalUrls = [];
  try {
    const response = await sendRequest(url);
    if (response.status === 200) {
      const sitemapContent = await response.text();
      const sitemapJson = xmlParser.xml2js(sitemapContent, { compact: true });
      if (sitemapJson.urlset && sitemapJson.urlset.url) {
        sitemapJson.urlset.url.forEach((url) => {
          if (url.loc && url.loc._text) {
            const loc = url.loc._text.trim();
            // console.log("loc", loc);
            if (validSitemapUrl(loc)) {
              urls = fetchSitemapUrls(loc);
              finalUrls.push(...urls);
            }
            if (validAboutUrl(loc)) {
              finalUrls.push(cleanUrl(loc));
            }
          }
        });
      }
    }
    // else {
    //   console.error(`Failed to Fetch Sitemap Urls ${url}: ${response.status}`);
    // }
    return finalUrls;
  } catch (error) {
    console.error("Error Stack:", error.stack);
    console.error(
      `An error occurred while fetching sitemap from ${url}: ${error}`
    );
    return finalUrls;
  }
}

async function fetchSitemapUrlsV2(base_url, url) {
  console.log("Fetching Sitemap Urls:", url);
  const spanElements = [];
  try {
    const response = await sendRequest(url);
    if (response.status === 200) {
      const htmlContent = await response.text();
      const dom = new JSDOM(htmlContent);
      const spans = dom.window.document.querySelectorAll("span");

      spans.forEach((span) => {
        const spanText = span.textContent.trim();
        console.log("spanText", spanText);
        if (spanText.includes(base_url)) {
          spanElements.push(spanText);
        }
      });
    }
    // else {
    //   console.error(`Failed to Fetch Sitemap Urls ${url}: ${response.status}`);
    // }
    return spanElements;
  } catch (error) {
    console.error("Error Stack:", error.stack);
    console.error(
      `An error occurred while fetching sitemap from ${url}: ${error}`
    );
    return spanElements;
  }
}

async function getRelatedUrls(baseUrl) {
  const updatedUrls = [];
  try {
    const sitemapUrls = sitemapTemplates.map((template) => baseUrl + template);
    for (const sitemapUrl of sitemapUrls) {
      const urls = await fetchSitemapUrls(sitemapUrl);
      // const urlsV2 = await fetchSitemapUrlsV2(baseUrl, sitemapUrl);
      updatedUrls.push(...urls);
    }
    const response = await sendRequest(baseUrl);
    if (response.status === 200) {
      const html = await response.text();
      const dom = new JSDOM(html, { url: baseUrl });
      const links = Array.from(dom.window.document.querySelectorAll("a[href]"));
      const modifiedUrl = baseUrl.split(".").slice(-2).join(".");
      const relatedUrls = links
        .filter((link) => {
          const href = link.getAttribute("href");
          return (
            href.startsWith("/") ||
            href.includes(baseUrl) ||
            href.includes(modifiedUrl)
          );
        })
        .map((link) => cleanUrl(link.href))
        .filter(validAboutUrl);
      updatedUrls.push(...relatedUrls);
    } else if (response.status === 202) {
      console.log(
        `Received 202 Accepted. Retrying in ${RETRY_DELAY / 1000} seconds...`
      );
      await sleep(RETRY_DELAY);
      return getRelatedUrls(baseUrl);
    } else {
      console.error(
        `Failed Request to Get Related urls ${baseUrl}: ${response.status}`
      );
    }
    return updatedUrls;
  } catch (error) {
    console.error("Error Stack:", error.stack);
    // console.error(`Failed to Get Related urls ${baseUrl}: ${error}`);
    return updatedUrls;
  }
}

async function getAboutUrlData(originalUrl) {
  try {
    const aboutUrls = new Set(await getRelatedUrls(originalUrl));
    let homePageText = await getAboutText(originalUrl);
    const urls = new Set();
    const aboutTexts = new Set();
    console.log(`About Urls for ${originalUrl}:`, aboutUrls);
    if (aboutUrls.size === 0) {
      console.log(`No url found for ${originalUrl}`);
      urls.add("No Url Found");
      aboutTexts.add("No Text Found");
    } else {
      for (const aboutUrl of aboutUrls) {
        console.log(`Processing About URL: ${aboutUrl}`);
        const aboutText = await getAboutText(aboutUrl);
        if (aboutText.split(" ").length > 20) {
          aboutTexts.add(aboutText.replace(/"/g, "`"));
        }
      }
      urls.add(...aboutUrls);
    }
    if (aboutTexts.size === 0) {
      aboutTexts.add("No Text Found");
    }
    if (urls.size === 0) {
      urls.add("No Url Found");
    }
    if (!homePageText) {
      homePageText = "No Text Found";
    }
    console.log(`Final Urls for ${originalUrl}:`, urls);

    return [
      homePageText,
      Array.from(urls).join("||"),
      Array.from(aboutTexts).join("||"),
    ];
  } catch (error) {
    console.error("Error Stack:", error.stack);
    // console.error(`Failed to Get About Url Data from ${originalUrl}: ${error}`);
    // console.log(error.stack);
    return ["No Text Found", "No Url Found", "No Text Found"];
  }
}

async function processUrl(url, companyId = 0) {
  const website = cleanUrl(url);
  let [homePageText, urls, aboutTexts] = await getAboutUrlData(website);
  // Check the homepage and About page data size and cap it near 4k characters
  if (homePageText.length > 4000) {
    homePageText = homePageText.substring(0, 4000) + "...";
  }
  if (aboutTexts.length > 4000) {
    aboutTexts = aboutTexts.substring(0, 4000) + "...";
  }

  // TODO: Use translation API to check if the data is in English
  const status =
    urls === "No Url Found" ||
    aboutTexts === "No Text Found" ||
    homePageText.indexOf("Cloudflare") !== -1
      ? "incomplete"
      : "completed";
  const data = {
    "Company ID": companyId,
    Website: website,
    "Home Page Text": homePageText,
    "About URLs": urls,
    "About Texts": aboutTexts,
    Status: status,
  };
  return data;
}

async function storeData(data) {
  try {
    // As we are reprocessing the data, we need to check if the data already exists
    let finalData = await prisma.aboutData.findFirst({
      where: {
        companyId: data["Company ID"],
      },
    });
    if (finalData && finalData.status !== "incomplete") {
      console.log("Data already exists:", finalData);
      await prisma.aboutData.update({
        where: {
          id: finalData.id,
        },
        data: {
          website: data["Website"],
          homepageData: data["Home Page Text"],
          aboutUrls: data["About URLs"],
          aboutData: data["About Texts"],
          status: data["Status"],
        },
      });
    } else {
      finalData = await prisma.aboutData.create({
        data: {
          companyId: data["Company ID"],
          website: data["Website"],
          homepageData: data["Home Page Text"],
          aboutUrls: data["About URLs"],
          aboutData: data["About Texts"],
          status: data["Status"],
        },
      });
    }

    // console.log("Data stored:", result);
    return finalData.id;
  } catch (error) {
    console.error("Error Stack:", error.stack);
    console.error("Error storing data:", error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

async function main({ company_url, company_id }) {
  try {
    // const { default: fetch } = await import('node-fetch');
    let data = await prisma.aboutData.findFirst({
      where: {
        companyId: company_id,
      },
    });
    if(!data){
      data = await processUrl(company_url, company_id);
      // console.log("Data:", data);
    const storedId = await storeData(data);
    }else{
      console.log("About Data already exists:");
    }
    // updateJobStatus(job_id, "completed");
    return data;
  } catch (error) {
    console.error("Error Stack:", error.stack);
    console.error("An error occurred:", error);
  }
}

module.exports = main;
