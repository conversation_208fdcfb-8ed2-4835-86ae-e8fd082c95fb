const prisma = require("../../database/prisma/getPrismaClient");
const runPromptChain = require("./runPromptChain");

async function callFeature(
  featureName,
  userInput,
  email,
  sellerName,
  productSlug,
  clientId
) {
  try {
    const feature = await prisma.feature.findFirst({
      where: { name: featureName },
      include: { prompts: true }, // include all prompt versions
    });

    if (!feature || feature.prompts.length === 0) {
      throw new Error("Feature not found or no prompts available.");
    }

    // Pick a random prompt
    const randomIndex = Math.floor(Math.random() * feature.prompts.length);
    const selectedPrompt = feature.prompts[randomIndex];

  

    console.log({selectedPrompt})
    //   Call to AI using LangChain Wrapper
    const result = await runPromptChain(selectedPrompt.content, userInput);
    // //   Update PromptLogs
    await prisma.promptLogs.create({
      data: {
        ftID: feature.id,
        promptId: selectedPrompt.id,
        cost: result.cost,
        output: result.output,
        tokenUsage: result.tokenUsage,
        email,
        sellerName,
        productSlug,
        clientId,
      },
    });

    return result;
  } catch (error) {
    console.error("Error in callFeature:", error);
    throw error;
  }
}

module.exports = callFeature;

// Test Run

async function Example() {
  try {
    const featureName = "comp-search-keyword";
    const userInput = "nike";
    const email = "<EMAIL>";
    const sellerName = "monke";
    const productSlug = "test-slug-1";
    const clientId = 1;

    const res = await callFeature(
      featureName,
      userInput,
      email,
      sellerName,
      productSlug,
      clientId
    );
    console.log("RESPONSE:", res)
  } catch (error) {
    console.log("ERROR:", error)
  }
};

Example();
