// sheets.js
const { google } = require("googleapis");
const path = require("path");
const cheerio = require("cheerio");
const { formatDateOnly, formatTimeOnly } = require("../../utils/utils");

// Load the service account key JSON file
const SERVICE_ACCOUNT_FILE = path.join(__dirname, "/credentials.json");

// Define the required scopes
const SCOPES = ["https://www.googleapis.com/auth/spreadsheets"]; // Adjust scope for read/write access

// Create a JWT client
const auth = new google.auth.GoogleAuth({
  keyFile: SERVICE_ACCOUNT_FILE,
  scopes: SCOPES,
});

function extractTextFromHTML(htmlString) {
  const $ = cheerio.load(htmlString);
  return $("body").text();
}

function cleanReply(reply) {
  reply = extractTextFromHTML(reply);
  // Remove automated response markers
  reply = reply.replace(/##- Please type your reply above this line -##/g, "");
  reply = reply.replace(/-- Please reply above this line --/g, "");

  // Remove quoted text and reply threads (e.g., "-----Original Message-----" or "On ... wrote:")
  reply = reply.replace(/-----Original Message-----.*$/gis, "");
  reply = reply.replace(/wrote:.*/gis, "");

  // Remove common email headers and footers
  reply = reply.replace(/From:.*|To:.*|Subject:.*|Date:.*$/gim, "");

  // Remove email signatures, disclaimers, and unrelated links
  reply = reply.replace(
    /(Best regards,|Thanks,|Sincerely,|Kind regards,|Regards,).*$/gis,
    ""
  );
  reply = reply.replace(
    /(To add additional comments, reply to this email).*$/gis,
    ""
  );
  reply = reply.replace(
    /(This email.*is confidential|Do not share|For.*visit.*https?:\/\/).*/gis,
    ""
  );
  // reply = reply.replace(/unsubscribe/gi, "");
  reply = reply.replace(/Thank you for your patience.*$/gis, "");
  reply = reply.replace(
    /We are currently experiencing a higher volume.*$/gis,
    ""
  );
  reply = reply.replace(/You can copy the following URL.*$/gis, "");
  reply = reply.replace(/Click here.*$/gis, "");
  reply = reply.replace(/https?:\/\/\S+/g, ""); // Remove URLs
  reply = reply.replace(/\[.*?\]/g, ""); // Remove bracketed content
  reply = reply.replace(/\*.*?\*/g, ""); // Remove emphasized text blocks
  // reply = reply.replace(/\bunsubscribe\b/gi, ""); // Remove "unsubscribe"
  reply = reply.replace(/Sent from my .*?\n/gi, ""); // Sent from mobile disclaimers
  reply = reply.replace(/(If you have any further questions).*$/gi, ""); // Generic closing statements

  // Remove excessive whitespace and normalize line breaks
  reply = reply.replace(/\n\s*\n/g, "\n").trim();

  // Keep only the main body content (remove overly technical or promotional blocks)
  reply = reply.replace(/We appreciate hearing from you.*$/gis, "");
  reply = reply.replace(/Links:------.*$/gis, "");
  reply = reply.replace(/A Customer Support Representative.*$/gis, "");

  // Remove text enclosed in ** and anything after the first *
  reply = reply.replace(/\*\*.*?\*\*/g, ""); // Remove text within double asterisks
  reply = reply.replace(/.*?\*[\s\S]*$/g, ""); // Remove everything after the first asterisk (*)

  // Remove everything within < > and after an email
  reply = reply.replace(/<[^>]*>/g, ""); // Remove text within <>
  // reply = reply.replace(
  //   /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}.*$/g,
  //   ""
  // ); // Remove everything after an email address
  reply = reply
    .replace(/&gt;/g, ">")
    .replace(/&lt;/g, "<")
    .replace(/&amp;/g, "&");

  return reply.trim();
}

// Function to append data to the spreadsheet
async function appendDataToSpreadsheet(data, user) {
  try {
    const client = await auth.getClient();
    const sheets = google.sheets({ version: "v4", auth: client });
    const { googleSheetURL, name } = user;
    console.log("master user :", user);
    const spreadsheetId = googleSheetURL.match(/\/d\/([a-zA-Z0-9-_]+)\//)[1];
    const range = "Sheet1!A1";

    const dataArray = [
      name || "Undefined",
      data.campaign_name || "Undefined",
      data.to_name || "Undefined",
      data.to_email || "Undefined",
      data?.lead_category?.new_name || "Undefined",
      // data?.reply_message?.html || "Undefined",
      cleanReply(data?.reply_message?.html || "Undefined"),
      formatDateOnly(data?.reply_message?.time) ||
        formatDateOnly(data?.sent_message?.time) ||
        "Undefined",
      formatTimeOnly(data?.reply_message?.time) ||
        formatDateOnly(data?.sent_message?.time) ||
        "Undefined",
    ];

    if (user?.id != 1) {
      dataArray.shift();
    }

    const resource = {
      values: [dataArray],
    };

    const response = await sheets.spreadsheets.values.append({
      spreadsheetId,
      range,
      valueInputOption: "RAW",
      resource,
    });

    console.log("Data appended successfully:", response.data);
    return response.data; // Return the successful response
  } catch (error) {
    console.error("Error appending data to the spreadsheet:", error);
    throw new Error(`Spreadsheet append failed: ${error.message}`); // Throw the error
  }
}
// Function to append data to spreadsheet with 3 retries ( 2s, 4s, 8s )
async function appendDataToSheetWithRetry(
  data,
  user,
  retries = 3,
  baseDelay = 2000
) {
  let attempt = 0;

  while (attempt < retries) {
    try {
      return await appendDataToSpreadsheet(data, user);
    } catch (error) {
      if (error.response?.status === 503) {
        attempt++;
        if (attempt >= retries) {
          throw new Error(
            "Google Sheets API unavailable after multiple retries."
          );
        }
        const delay = baseDelay * Math.pow(2, attempt - 1); // 2s, 4s, 8s
        console.warn(
          `Retrying in ${delay / 1000} seconds... (${attempt}/${retries})`
        );
        await new Promise((res) => setTimeout(res, delay));
      } else {
        console.error(
          `Error appending data for  ${user}: ${error.message} `,
          error
        );
        return null;
      }
    }
  }
}

module.exports = {
  appendDataToSpreadsheet,
  cleanReply,
  appendDataToSheetWithRetry,
};
