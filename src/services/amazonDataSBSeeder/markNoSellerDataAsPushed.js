const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function markNoSellerDataAsPushed() {
    try {
        console.log('🚀 Starting to mark Amazon product data without seller information as pushed...');
        console.log(`⏰ Started at: ${new Date().toISOString()}`);

        // Find all Amazon product data records that don't have seller information
        const recordsToMark = await prisma.amazonProductData.findMany({
            where: {
                pushedToSB: false,
                company: {
                    storeFrontURL: ""
                }
            },
            include: {
                company: {
                    select: {
                        id: true,
                        name: true,
                        storeFrontURL: true
                    }
                }
            }
        });

        console.log(`📊 Found ${recordsToMark.length} records without seller information to mark as pushed`);

        if (recordsToMark.length === 0) {
            console.log('✅ No records found without seller information. All records are properly configured.');
            return;
        }

        // Update all records to mark them as pushed
        // const updateResult = await prisma.amazonProductData.updateMany({
        //     where: {
        //         pushedToSB: false,
        //         company: {
        //             storeFrontURL: ""
        //         }
        //     },
        //     data: {
        //         pushedToSB: true,
        //         updatedAt: new Date()
        //     }
        // });

        // console.log(`✅ Successfully marked ${updateResult.count} records as pushedToSB: true`);
        console.log(`📝 Updated records for companies without storeFrontURL`);

        // Log some examples for verification
        const sampleRecords = recordsToMark.slice(0, 5);
        console.log('\n📋 Sample records that were marked:');
        sampleRecords.forEach((record, index) => {
            console.log(`${index + 1}. AmazonProductData ID: ${record.id}, Company: ${record.company.name}, StoreFrontURL: "${record.company.storeFrontURL}"`);
        });

        if (recordsToMark.length > 5) {
            console.log(`... and ${recordsToMark.length - 5} more records`);
        }

        console.log('\n🎉 Process completed successfully!');
        console.log(`⏰ Finished at: ${new Date().toISOString()}`);

    } catch (error) {
        console.error('❌ Error marking records as pushed:', error);
        throw error;
    } finally {
        await prisma.$disconnect();
    }
}

// Run the function if this file is executed directly
if (require.main === module) {
    markNoSellerDataAsPushed()
        .then(() => {
            console.log('✅ Script completed successfully');
            process.exit(0);
        })
        .catch((error) => {
            console.error('❌ Script failed:', error);
            process.exit(1);
        });
}

module.exports = { markNoSellerDataAsPushed }; 