// Load environment variables from .env file
require("dotenv").config();

const { PrismaClient } = require("@prisma/client");
const {
    targetDbConfig,
    testConnection,
    checkTargetTable,
} = require("./amazonProductDataToProductConfig");

const prisma = new PrismaClient();

// Function to get target database connection
async function getTargetDbConnection() {
    const { Client } = require('pg');

    let client;

    // Use connection string if available, otherwise fallback to parsed config
    if (process.env.TARGET_DATABASE_URL) {
        client = new Client({
            connectionString: process.env.TARGET_DATABASE_URL,
            ssl: {
                rejectUnauthorized: false,
                require: true,
                ca: undefined
            }
        });
    } else {
        // Fallback to parsed configuration
        let freshTargetDbConfig = {
            host: 'localhost',
            port: 5432,
            database: 'target_database',
            username: 'username',
            password: 'password',
            ssl: false,
        };

        if (process.env.TARGET_DB_HOST) {
            freshTargetDbConfig = {
                host: process.env.TARGET_DB_HOST,
                port: parseInt(process.env.TARGET_DB_PORT) || 5432,
                database: process.env.TARGET_DB_NAME || 'target_database',
                username: process.env.TARGET_DB_USER || 'username',
                password: process.env.TARGET_DB_PASSWORD || 'password',
                ssl: process.env.TARGET_DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
            };
        }

        client = new Client(freshTargetDbConfig);
    }

    await client.connect();
    return client;
}

// Function to get products without seller data
async function getProductsWithoutSellerData() {
    const { Client } = require('pg');

    let client;

    // Use connection string if available, otherwise fallback to parsed config
    if (process.env.TARGET_DATABASE_URL) {
        client = new Client({
            connectionString: process.env.TARGET_DATABASE_URL,
            ssl: {
                rejectUnauthorized: false,
                require: true,
                ca: undefined
            }
        });
    } else {
        // Fallback to parsed configuration
        let freshTargetDbConfig = {
            host: 'localhost',
            port: 5432,
            database: 'target_database',
            username: 'username',
            password: 'password',
            ssl: false,
        };

        if (process.env.TARGET_DB_HOST) {
            freshTargetDbConfig = {
                host: process.env.TARGET_DB_HOST,
                port: parseInt(process.env.TARGET_DB_PORT) || 5432,
                database: process.env.TARGET_DB_NAME || 'target_database',
                username: process.env.TARGET_DB_USER || 'username',
                password: process.env.TARGET_DB_PASSWORD || 'password',
                ssl: process.env.TARGET_DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
            };
        }

        client = new Client(freshTargetDbConfig);
    }

    try {
        await client.connect();

        // Find products that have null or empty seller information
        const result = await client.query(`
            SELECT id, url, brand_name, "sellerId", marketplace, "sellerUrl", "createdAt"
            FROM products 
            WHERE "sellerId" IS NULL 
               OR "sellerId" = '' 
               OR "sellerUrl" IS NULL 
               OR "sellerUrl" = ''
            ORDER BY id
        `);

        console.log(`Found ${result.rows.length} products without seller data`);
        return result.rows;
    } catch (error) {
        console.error('Error fetching products without seller data:', error.message);
        return [];
    } finally {
        await client.end();
    }
}

// Function to delete products without seller data
async function deleteProductsWithoutSellerData() {
    let targetDb = null;

    try {
        console.log('=== Deleting Products Without Seller Data ===\n');

        // Test connection and table existence
        console.log('1. Testing database connection...');
        const connectionOk = await testConnection();
        if (!connectionOk) {
            throw new Error('Failed to connect to target database');
        }
        console.log('✅ Database connection successful');

        console.log('\n2. Checking target table...');
        const tableExists = await checkTargetTable();
        if (!tableExists) {
            throw new Error('Target table "products" does not exist');
        }
        console.log('✅ Target table exists');

        // Get target database connection
        targetDb = await getTargetDbConnection();

        // Get products that need to be deleted
        console.log('\n3. Finding products without seller data...');
        const productsToDelete = await getProductsWithoutSellerData();

        if (productsToDelete.length === 0) {
            console.log('✅ No products found without seller data');
            return;
        }

        console.log(`Found ${productsToDelete.length} products to delete`);

        // Show sample of products to be deleted
        console.log('\n4. Sample of products to be deleted:');
        const sampleSize = Math.min(5, productsToDelete.length);
        for (let i = 0; i < sampleSize; i++) {
            const product = productsToDelete[i];
            console.log(`   ID: ${product.id}, URL: ${product.url}, Brand: ${product.brand_name}, SellerID: ${product.sellerId || 'NULL'}`);
        }

        if (productsToDelete.length > sampleSize) {
            console.log(`   ... and ${productsToDelete.length - sampleSize} more`);
        }

        // Ask for confirmation (in a real scenario, you might want to add a confirmation prompt)
        console.log('\n5. Proceeding with deletion...');

        // Delete products in batches
        const batchSize = 100;
        let totalDeleted = 0;
        let totalErrors = 0;

        for (let i = 0; i < productsToDelete.length; i += batchSize) {
            const batch = productsToDelete.slice(i, i + batchSize);
            const batchNumber = Math.floor(i / batchSize) + 1;
            const totalBatches = Math.ceil(productsToDelete.length / batchSize);

            console.log(`\nProcessing batch ${batchNumber}/${totalBatches} (${batch.length} products)`);

            try {
                // Use a transaction for the batch
                await targetDb.query('BEGIN');

                const deleteQuery = `
                    DELETE FROM products 
                    WHERE id = ANY($1)
                `;

                const productIds = batch.map(p => p.id);
                const result = await targetDb.query(deleteQuery, [productIds]);

                await targetDb.query('COMMIT');

                console.log(`✅ Deleted ${result.rowCount} products from batch ${batchNumber}`);
                totalDeleted += result.rowCount;

            } catch (error) {
                await targetDb.query('ROLLBACK');
                console.error(`❌ Error deleting batch ${batchNumber}:`, error.message);
                totalErrors += batch.length;
            }

            // Small delay between batches
            if (i + batchSize < productsToDelete.length) {
                await new Promise(resolve => setTimeout(resolve, 100));
            }
        }

        // Summary
        console.log('\n=== Deletion Summary ===');
        console.log(`📊 Total products found without seller data: ${productsToDelete.length}`);
        console.log(`✅ Successfully deleted: ${totalDeleted} products`);
        console.log(`❌ Errors: ${totalErrors} products`);
        console.log(`📊 Remaining products without seller data: ${productsToDelete.length - totalDeleted}`);

    } catch (error) {
        console.error('❌ Deletion failed:', error);
        throw error;
    } finally {
        if (targetDb) {
            await targetDb.end();
        }
        await prisma.$disconnect();
    }
}

// Function to preview what would be deleted (dry run)
async function previewProductsToDelete() {
    try {
        console.log('=== Preview: Products Without Seller Data ===\n');

        // Test connection
        const connectionOk = await testConnection();
        if (!connectionOk) {
            throw new Error('Failed to connect to target database');
        }

        // Get products that would be deleted
        const productsToDelete = await getProductsWithoutSellerData();

        if (productsToDelete.length === 0) {
            console.log('✅ No products found without seller data');
            return;
        }

        console.log(`Found ${productsToDelete.length} products without seller data`);

        // Show detailed breakdown
        console.log('\n=== Breakdown ===');

        const nullSellerId = productsToDelete.filter(p => p.sellerId === null || p.sellerId === '').length;
        const nullSellerUrl = productsToDelete.filter(p => p.sellerUrl === null || p.sellerUrl === '').length;
        const bothNull = productsToDelete.filter(p =>
            (p.sellerId === null || p.sellerId === '') &&
            (p.sellerUrl === null || p.sellerUrl === '')
        ).length;

        console.log(`📊 Products with null/empty sellerId: ${nullSellerId}`);
        console.log(`📊 Products with null/empty sellerUrl: ${nullSellerUrl}`);
        console.log(`📊 Products with both null/empty: ${bothNull}`);

        // Show sample products
        console.log('\n=== Sample Products ===');
        const sampleSize = Math.min(10, productsToDelete.length);
        for (let i = 0; i < sampleSize; i++) {
            const product = productsToDelete[i];
            console.log(`${i + 1}. ID: ${product.id}`);
            console.log(`   URL: ${product.url}`);
            console.log(`   Brand: ${product.brand_name}`);
            console.log(`   SellerID: ${product.sellerId || 'NULL'}`);
            console.log(`   SellerURL: ${product.sellerUrl || 'NULL'}`);
            console.log(`   Created: ${product.createdAt}`);
            console.log('');
        }

        if (productsToDelete.length > sampleSize) {
            console.log(`... and ${productsToDelete.length - sampleSize} more products`);
        }

        console.log('\n⚠️  This is a preview. Use --delete to actually delete these products.');

    } catch (error) {
        console.error('❌ Preview failed:', error);
    } finally {
        await prisma.$disconnect();
    }
}

// Main execution
async function main() {
    const args = process.argv.slice(2);

    if (args.length === 0) {
        console.log('Usage:');
        console.log('  node deleteProductsWithoutSellerData.js --preview');
        console.log('  node deleteProductsWithoutSellerData.js --delete');
        return;
    }

    const command = args[0];

    if (command === '--preview') {
        await previewProductsToDelete();
    } else if (command === '--delete') {
        await deleteProductsWithoutSellerData();
    } else {
        console.log('Invalid command. Use --preview or --delete');
    }
}

// Export functions for testing
module.exports = {
    deleteProductsWithoutSellerData,
    previewProductsToDelete,
    getProductsWithoutSellerData
};

// Run if called directly
if (require.main === module) {
    main().catch(console.error);
} 