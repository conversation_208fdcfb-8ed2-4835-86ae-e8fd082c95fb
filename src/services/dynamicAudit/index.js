const getProductTitleReport = require("./productTitleReport");
const getBulletPointReport = require("./bulletPointReport");
const getImageReport = require("./imageReport");
const getAPlusContentReport = require("./aPlusContentReport");
const getVideoReport = require("./videoReport");
const getReviewReport = require("./reviewReport");
const getRatingsReport = require("./ratingsReport");
const getAdsReport = require("./adsReport");
const getStoreFrontReport = require("./storeFrontReport");
const { getFilePathFromRegionAndClientId } = require("../../utils/yamlParser");
const getBrandingReport = require("./brandingReport");

async function getDynamicReport(data, clientId) {
  // Get order from yml file for the types of reviews
  let report = [];
  const ymlPath = getFilePathFromRegionAndClientId(clientId);

  await getProductTitleReport(data, report, clientId, ymlPath);
  // console.log("Product Title Report Generated: ");
  getBulletPointReport(data, report, clientId, ymlPath);
  // console.log("Bullet Point Report Generated: ");
  getImageReport(data, report, clientId, ymlPath);
  // console.log("Image Report Generated: ");
  getAPlusContentReport(data, report, clientId, ymlPath);
  // console.log("A+ Content Report Generated: ");
  getVideoReport(data, report, clientId, ymlPath);
  // console.log("Video Report Generated: ");
  getReviewReport(data, report, clientId, ymlPath);
  // console.log("Review Report Generated: ");
  getRatingsReport(data, report, clientId, ymlPath);
  // console.log("Ratings Report Generated: ");
  // getAdsReport(data, report,clientId);
  // console.log("Ads Report Generated: ");
  getStoreFrontReport(data, report, clientId, ymlPath);
  getBrandingReport(data, report, clientId, ymlPath);
  return report
}

module.exports = getDynamicReport;
