const { parseYamlReport } = require("../../utils/yamlParser");
function getBrandingReport(data, report, clientId, ymlPath) {
    if (!data.productData?.[0]?.images) {
        return;
    }

    const reportData = {
        videosCount: data.productData[0].images.noOfVideos,
        prospectRevenue:
            Math.round(data.productData[0].price * data.productData[0].sales) || 0,
    };

    const brandReports = parseYamlReport("brandReport", reportData, ymlPath);
    report.push(...brandReports);
}

module.exports = getBrandingReport;
