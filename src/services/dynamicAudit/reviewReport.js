const { parseYamlReport } = require("../../utils/yamlParser");
function getReviewReport(data, report, clientId, ymlPath) {
  if (!data.productData?.[0]?.review) {
    return;
  }

  const reportData = {
    calculateNextIdealNumber: (currentNumber) => {
      const idealNumbers = [
        30, 70, 100, 300, 700, 1000, 3000, 5000, 7000, 10000, 30000, 50000,
        70000, 100000,
      ];
      for (let i = 0; i < idealNumbers.length; i++) {
        if (currentNumber < idealNumbers[i]) {
          return idealNumbers[i];
        }
      }
      return idealNumbers[idealNumbers.length - 1];
    },
    reviewCount: data.productData[0].review.totalReviewCountInt,
    lowStarReviews: data.productData[0].review.reviewPerStar
      .filter((review) => "1starReview" in review || "2starReview" in review)
      .reduce((acc, review) => {
        const [_, value] = Object.entries(review)[0];
        acc += value;
        return acc;
      }, 0),
  };

  const reviewReports = parseYamlReport("reviewReport", reportData, ymlPath);
  report.push(...reviewReports);
}

module.exports = getReviewReport;
