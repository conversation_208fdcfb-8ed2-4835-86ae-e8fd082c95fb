const {parseYamlReport} = require("../../utils/yamlParser");
function getVideoReport(data, report, clientId,ymlPath) {
  if (!data.productData?.[0]?.images) {
    return;
  }

  const reportData = {
    videosCount: data.productData[0].images.noOfVideos,
    prospectRevenue:
      Math.round(data.productData[0].price * data.productData[0].sales) || 0,
  };

  const videoReports = parseYamlReport("videoReport", reportData,ymlPath);
  report.push(...videoReports);
}

module.exports = getVideoReport;
