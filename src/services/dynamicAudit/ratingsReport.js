const { parseYamlReport } = require("../../utils/yamlParser");
function getRatingsReport(data, report, clientId, ymlPath) {
  if (!data.productData?.[0]?.rating) {
    return;
  }

  const reportData = {
    rating: data.productData[0].rating.rating,
    ratingComesAs: data.productData[0].rating.ratingComesAs,
    lowStarReviews: data.productData[0].review.reviewPerStar
      .filter((review) => "1starReview" in review || "2starReview" in review)
      .reduce((acc, review) => {
        const [_, value] = Object.entries(review)[0];
        acc += value;
        return acc;
      }, 0),
  };

  const ratingReports = parseYamlReport("ratingsReport", reportData, ymlPath);
  report.push(...ratingReports);
}

module.exports = getRatingsReport;
