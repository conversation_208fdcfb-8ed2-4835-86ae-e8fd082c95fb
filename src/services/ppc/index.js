const getPDPAdSearch = require("../../services/ppc/getPDPAdSearch");
const fs = require("fs");
const stringSimilarity = require("string-similarity");
const getTargetURL = require("../../utils/multicountry/getTargetURL");
const { completionFactory } = require("../scrapeGPT/factory");
const getSponsoredListing = require("./getSponsoredListing");
const getCompetitorSearch = require("./getCompetitorSearch");
const {
  getBrandedSearchCopy,
  getBrandedKeywordSearchCopy,
  getNonBrandedKeywordSearchCopy,
  getCompetitorSearchCopy,
  getPDPAdSearchCopy,
} = require("./ppcCopy");
const { getBrowser } = require("../../utils/puppeteer/browserHelper");
// const { aavrani, freego, everybot } = require("./sampleCSV/ppcTestCSV");

//Todo- Brand = seller
async function getPpcData(csvData, clientId) {
  const companyId = csvData.companyId;
  const asin = csvData.prospectDetails.asin;
  const sellerName = csvData.companyName; //sellerName
  const slug = csvData.productSlug;
  const amazonAudit = csvData.amazonAudit;
  //Get brandName from amazonAudit
  const extractBrand = (amazonAudit) =>
    amazonAudit?.productData?.length > 0
      ? amazonAudit.productData[0].brandName
      : "";
  const brandName = extractBrand(amazonAudit);
  // const brandName = csvData.brandName; //open it incase using example()
  const browser = await getBrowser();
  console.log("Getting PPC Audit for Brand:", sellerName);
  try {
    let TARGET_URL;
    try {
      TARGET_URL = await getTargetURL(companyId);
    } catch (error) {
      console.error("Error fetching target URL:", error);
      TARGET_URL = ""; // Default fallback if needed
    }

    let brandedData = { text: "", count: 0, hasSponsored: false };
    let brandedKeywordData = { text: "", count: 0, hasSponsored: false };
    let nonBrandedKeywordData = { text: "", count: 0, hasSponsored: false };
    let competitorBrandedData = { text: "", count: 0, hasSponsored: false };
    let ppdData = { text: "", count: 0, hasSponsored: false };
    let ppcAudit = {};

    console.log("-------Branded Search-------");
    try {
      const normalizedSeller = sellerName.toLowerCase().replace(/[-_\s]+/g, "");
      const normalizedBrand = brandName.toLowerCase().replace(/[-_\s]+/g, "");

      /// First check if brand exists in seller name
      const brandExistsInSeller = normalizedSeller.includes(normalizedBrand);

      // Only do similarity check if brand isn't found in seller name
      let searchTerm;
      if (brandExistsInSeller) {
        searchTerm = brandName;
      } else {
        const similarity = stringSimilarity.compareTwoStrings(
          normalizedSeller,
          normalizedBrand
        );
        const SIMILARITY_THRESHOLD = 0.8;
        searchTerm =
          similarity >= SIMILARITY_THRESHOLD
            ? brandName
            : `${sellerName} ${brandName}`.trim();
      }
      brandedData = await getSponsoredListing(
        asin,
        sellerName, //Prospect sellerName
        brandName,
        companyId,
        TARGET_URL,
        searchTerm,
        "brand"
      );

      brandedData.text = getBrandedSearchCopy(
        brandedData.isSponsoredInFirstRow
      );
      ppcAudit.BrandedSearch = brandedData;
    } catch (error) {
      console.error("Error fetching branded search data:", error);
    }
    console.log(
      "KEYWORDS IN PPC:",
      csvData.auditMailData.brandedKeyword,
      "####",
      csvData.auditMailData.nonBrandedKeyword
    );
    if (csvData.auditMailData.brandedKeyword) {
      console.log("-------Branded Keyword Search-------");
      try {
        brandedKeywordData = await getSponsoredListing(
          asin,
          sellerName, //Prospect sellerName
          brandName,
          companyId,
          TARGET_URL,
          csvData.auditMailData.brandedKeyword,
          "brandedKeyword"
        );
        brandedKeywordData.text = getBrandedKeywordSearchCopy(
          brandedKeywordData.isSponsoredInFirstRow,
          csvData.auditMailData.brandedKeyword,
          brandedKeywordData.isProspectSponsored
        );
        ppcAudit.BrandedKeywordSearch = brandedKeywordData;
      } catch (error) {
        console.error("Error fetching branded keyword search data:", error);
      }
    }

    if (csvData.auditMailData.nonBrandedKeyword) {
      console.log("-------Non-Branded Keyword Search-------");
      try {
        nonBrandedKeywordData = await getSponsoredListing(
          asin,
          sellerName, //Prospect sellerName
          brandName,
          companyId,
          TARGET_URL,
          csvData.auditMailData.nonBrandedKeyword,
          "nonBrandedKeyword"
        );
        nonBrandedKeywordData.text = getNonBrandedKeywordSearchCopy(
          nonBrandedKeywordData.isSponsoredInFirstRow,
          brandedKeywordData.isSponsoredInFirstRow,
          csvData.auditMailData.nonBrandedKeyword,
          nonBrandedKeywordData.isProspectSponsored
        
        );
        ppcAudit.nonBrandedKeywordSearch = nonBrandedKeywordData;
      } catch (error) {
        console.error("Error fetching non-branded keyword search data:", error);
      }
    }

    const competitorBrand = csvData.competitorDetails.name;
    if (competitorBrand) {
      console.log("-------Competitor Branded Search-------");
      try {
        competitorBrandedData = await getCompetitorSearch(
          asin,
          competitorBrand,
          companyId,
          TARGET_URL,
          competitorBrand,
          "competitorBrandedSearch",
          sellerName, //Prospect sellerName
          brandName
        );
        competitorBrandedData.text = getCompetitorSearchCopy(
          competitorBrandedData.isSponsoredInFirstRow,
          brandedKeywordData.isSponsoredInFirstRow,
          competitorBrandedData.containsOnlyCompetitor,
          competitorBrandedData.containsProspectBrand,
          competitorBrand
        );
        ppcAudit.competitorBrandedData = competitorBrandedData;
      } catch (error) {
        console.error("Error fetching competitor branded search data:", error);
      }
    }

    console.log("-------PDP ad Search-------");
    try {
      ppdData = await getPDPAdSearch(
        asin,
        sellerName,
        brandName,
        companyId,
        TARGET_URL
      );
      ppdData.text = getPDPAdSearchCopy(ppdData.hasSponsored, ppdData.count);
      ppcAudit.ProspectPDPImage = ppdData;
    } catch (error) {
      console.error("Error fetching PDP ad search data:", error);
    }
    return ppcAudit;
  } catch (error) {
    console.log("Error in PPC:", error);
  }
}

module.exports = getPpcData;

async function Example(params) {
  try {
    // const csvData = {
    //   companyId: 22,
    //   companyName: "aavrani", //sellerName
    //   brandName: "aavrani",
    //   prospectDetails: {
    //     asin: "B0D3M7GSX2",
    //     revenue: 2030,
    //     productName:
    //       "AAVRANI Hair & Scalp Recovery Oil - Rosemary, Bond Complex, & Amla Treatment for Damaged Hair, Frizz Control, & Hair Growth - Vegan - 1.69 Fl Oz",
    //     revenueSource: "The 30-day sales number on Amazon",
    //     productAmazonURL: "https://www.amazon.com/dp/B0D3M7GSX2",
    //     humanizedProspectName: "aavrani",
    //     humanizedProspectProductTitle: "hair and scalp recovery oil",
    //   },
    //   auditMailData: {
    //     brandedKeyword: "aavrani cream",
    //     nonBrandedKeyword:"hair and scalp recovery oil"
    //   },
    //   competitorDetails: {
    //     asin: "B0CQ3GXLJ5",
    //     name: "sky organics",
    //     status: "Success",
    //     revenue: 14107.18,
    //     productName:
    //       "Sky Organics - Organic Rosemary Oil with Strong Roots - Hair Growth Serum with Macadamia, Jojoba & Essential Oils for Scalp - Natural Beauty and Hair Care - 2 fl oz",
    //     productAmazonURL: "https://www.amazon.com/dp/B0CQ3GXLJ5",
    //     humanizedCompCompanyName: "sky organics",
    //     humanizedCompProductTitle: "organic rosemary hair growth serum",
    //   },
    // };
    const csvData = everybot;
    const response = await getPpcData(csvData, 1);
    fs.writeFileSync("ppcReport_everybot.json", JSON.stringify(response));
    console.log("PPC RESPONSE:", response);
  } catch (error) {
    console.log("ERROR:", error);
  }
}

// console.log(freego)

// Example();
