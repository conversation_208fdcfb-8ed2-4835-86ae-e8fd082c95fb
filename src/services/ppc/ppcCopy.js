function getBrandedSearchCopy(hasSponsored) {
  if (hasSponsored) {
    return "Some competitors are running ads against your brand name and capturing your market share. I would suggest allocating some of the ads budget to defensive ads to protect your branded search. Please see the screenshot below:";
  } else {
    return "";
  }
}

function getNonBrandedKeywordSearchCopy(hasSponsored,hasBrandedKeyword, keyword, isProspectSponsored) {
  if (hasSponsored && keyword && hasBrandedKeyword && !isProspectSponsored) {
    return `I also searched for “${keyword}” & noticed your competitors are at the top for both branded & non-branded search terms related to your product. I suggest relooking at your keyword strategy and ensuring that you are bidding competitively on this shopper search term.`;
  } else if (hasSponsored && keyword && !isProspectSponsored) {
    return `I searched for “${keyword}” and saw competitors running ads against this search term. There is a high likelihood that shoppers who are actively searching for your product will end up clicking on your competitor's ads and purchasing from them instead resulting in lost sales. I would suggest that you increase your budget on generic ads to increase your product visibility and market share.`;
  } else {
    return "";
  }
}

function getPDPAdSearchCopy(hasSponsored, noOfSpots) {
  if (hasSponsored && noOfSpots) {
    return `There are ${noOfSpots} ad placements on your PDP that are being bid on by competitors. They are highlighted below.
I would suggest bidding on these placements (defensive ads) to avoid competitors advertising their products, that might have higher reviews or a lower price, on your PDP. This will prevent losing a potential customer to another competitor’s PDP and possibly losing a sale. It is also an opportunity to increase cross-selling of your catalogue, ultimately increasing your shopper basket size. 
By neglecting defensive advertising, you stand a chance of losing shoppers to competitors who are targeting you.
`;
  } else {
    return "";
  }
}

function getBrandedKeywordSearchCopy(hasSponsored, keyword, isProspectSponsored) {
  if (hasSponsored && !isProspectSponsored) {
    return `I searched for ”${keyword}” and saw competitors running ads against this search term. Imagine the customers who are actively searching for your product but end up buying from the competitor because the competitor is paying to appear in this search results and you are not.`;
  } else {
    return "";
  }
}

function getCompetitorSearchCopy(
  hasSponsored,
  hasBrandedKeyword,
  containsOnlyCompetitor,
  containsProspectBrand,
  compName
) {
  if (hasSponsored && !containsOnlyCompetitor && !containsProspectBrand) {
    return `You're not targeting the ${compName} while other competitors are at the top and stealing their traffic.`;
  } else if (hasSponsored && containsOnlyCompetitor && hasBrandedKeyword) {
    return `“${compName}” has very strong defensive campaigns and no competitors can target their branded keywords, you should do the same. This way they keep their revenue from being eroded by their competitor. `;
  } else {
    return "";
  }
}

module.exports = {
  getBrandedKeywordSearchCopy,
  getBrandedSearchCopy,
  getCompetitorSearchCopy,
  getNonBrandedKeywordSearchCopy,
  getPDPAdSearchCopy,
};

// console.log("BRANDED TEXT:", getBrandedSearchCopy(true));
// console.log("-----------------------------------------")
// console.log("BRANDED KEYWORD SEARCH TEXT:", getBrandedKeywordSearchCopy(true, "aavrani cream"));
// console.log("-----------------------------------------");
// console.log("COMPETITOR SEARCH TEXT:", getCompetitorSearchCopy(true, false, false, "nike"));
// console.log("-----------------------------------------");
// console.log("PDPD SEARCH TEXT:", getPDPAdSearchCopy(true, 6));
// console.log("-----------------------------------------");
// console.log("NON BRANDED KEYWORD SEARCH:",getNonBrandedKeywordSearchCopy(true, "cream"));
