const getTargetURL = require("../../utils/multicountry/getTargetURL");
const { closePages } = require("../../utils/puppeteer/browserHelper");
const connectPuppeteer = require("../../utils/puppeteer/index");
const { uploadImage, getS3Url } = require("../aws/s3");
const { selectors } = require("../scrapeAmazon/selectors");
const fuzzysort = require("fuzzysort");
const { parse } = require("json2csv");
const fs = require("fs");
const path = require("path");
const { getHtmlByProxy } = require("../../utils/getHtmlByProxy");

const IMAGE_FOLDER = "pdptest/";

async function getSponsoredListing(
  asin,
  sellerName,
  brand,
  company_id,
  TARGET_URL,
  searchTerm,
  file_name
) {
  let browser;
  let page;
  try {
    if (!TARGET_URL) {
      TARGET_URL = await getTargetURL(company_id);
    }
    ({ browser, page } = await connectPuppeteer(
      `${TARGET_URL}/s?k=${searchTerm}`,
      undefined,
      company_id
    ));

    // Save the HTML content of the page
    // const htmlContent = await page.content();
    // const htmlFilePath = path.join(
    //   __dirname,
    //   `${file_name}_${brand}_01_search.html`
    // );
    // fs.writeFileSync(htmlFilePath, htmlContent, "utf-8");
    // console.log(`HTML saved to ${htmlFilePath}`);

    await page.evaluate(() => {
      const input = document.querySelector("#twotabsearchtextbox");
      const div = document.createElement("div");
      div.style.cssText = `
                position: absolute;
                visibility: hidden;
                height: 0;
                width: 0;
                overflow: scroll;
                white-space: pre;
                height:${window.getComputedStyle(input).height};    
                font-size: ${window.getComputedStyle(input).fontSize};
            `;
      document.body.appendChild(div);
      window.invisibleDiv = div;
    });

    const divWidth = await page.evaluate((searchTerm) => {
      window.invisibleDiv.textContent += searchTerm;
      const input = document.querySelector("#twotabsearchtextbox");
      const inputWidth = input.offsetWidth;
      const divWidth = Math.min(window.invisibleDiv.scrollWidth, inputWidth);
      return divWidth;
    }, searchTerm);

    await page.evaluate((divWidth) => {
      const input = document.querySelector("#twotabsearchtextbox");
      const redBorder = document.createElement("div");
      const inputRect = input.getBoundingClientRect();
      redBorder.style.cssText = `
                position: absolute;
                top: ${inputRect.top + 3}px;
                left: ${inputRect.left + 3}px;
                height: 32px;
                width: ${divWidth}px;
                border:2px solid red;
                z-index: 9999999999;
            `;
      document.body.appendChild(redBorder);
    }, divWidth);

    const sponsoredElements = await page.evaluate((brand) => {
      const topSponsoredBanners = Array.from(
        document.querySelectorAll(".AdHolder.s-flex-full-width")
      );
      const sponsoredBanners = Array.from(
        document.querySelectorAll('[data-component-type="s-sponsored-banner"]')
      );
      const sponsoredSections = Array.from(
        document.querySelectorAll('[data-component-type="s-search-result"]')
      ).filter((el) =>
        el.querySelector('[data-component-type="s-sponsored-label-info"]')
      );
      const sponsoredProducts = Array.from(
        document.querySelectorAll(
          '[data-component-type="s-impression-counter"]'
        )
      ).filter((el) => el.querySelector(".puis-sponsored-label-text"));
      const sponsoredCarousels = Array.from(
        document.querySelectorAll(".s-widget-container")
      )
        .filter((el) => el.querySelector(".s-widget-sponsored-label-text"))
        .filter((el) =>
          el.querySelector('[data-component-type="s-impression-counter"]')
        );
      const sponsoredBanners3 = Array.from(
        document.querySelectorAll(".sb-video-creative")
      ).filter(
        (el) =>
          !el
            .querySelector('[data-type="productTitle"] .a-link-normal')
            ?.textContent?.toLowerCase()
            ?.includes(brand?.toLowerCase())
      );
      const sponsoredBanners2 = Array.from(
        document.querySelectorAll(
          '[data-component-type="sbv-video-single-product"]'
        )
      ).filter((el) => {
        const componentProps = JSON.parse(el?.dataset?.componentProps);
        if (componentProps?.videoType == "sponsored") {
          if (
            Array.from(
              el?.querySelectorAll(
                "span.a-size-medium.a-color-base.a-text-normal,span.a-size-base-plus.a-color-base"
              )
            ).some((span) =>
              span.textContent.toLowerCase().includes(brand.toLowerCase())
            )
          ) {
            return false;
          }
          return true;
        }
      });
      return [
        ...topSponsoredBanners,
        ...sponsoredBanners,
        ...sponsoredSections,
        ...sponsoredProducts,
        ...sponsoredCarousels,
        ...sponsoredBanners2,
        ...sponsoredBanners3,
      ].map((el) => {
        const rect = el.getBoundingClientRect();
        const differentSelectors = `.a-size-base-plus.a-color-base.a-text-normal,span.a-size-medium.a-color-base.a-text-normal,span.a-size-base-plus.a-color-base,span.a-size-medium.a-color-base,.a-size-medium.a-spacing-none.a-color-base.a-text-normal,.AdHolder.s-flex-full-width`;
        // const differentSelectors = `.puis-sponsored-label-text`

        let productTitle = [];

        if (
          el.classList.contains("AdHolder") &&
          el.classList.contains("s-flex-full-width")
        ) {
          // Target span elements with class "a-truncate" and get their text
          const truncateSpans = el.querySelectorAll("span.a-truncate, #ctaContainer");
          productTitle = Array.from(truncateSpans)
            .map((span) => span.innerText?.trim()?.toLowerCase())
            .filter((text) => text);
        } else {
          // Original logic for other elements
          productTitle = Array.from(
            el.querySelectorAll(differentSelectors)
          )?.map((el) => el.textContent?.toLowerCase() || "");
        }

        // const productTitle = Array.from(
        //   el.querySelectorAll(
        //     `[data-component-type="s-impression-counter"] ${differentSelectors},${differentSelectors}`
        //   )
        // )?.map((el) => el.textContent?.toLowerCase() || "");
        return {
          x: rect.x,
          y: rect.y,
          width: rect.width,
          height: rect.height,
          productTitle,
          containsBrandOrSeller: false,
          element: el,
        };
      });
    }, brand);
    // console.log(`Sponsored element: ${JSON.stringify(sponsoredElements, null, 2)}`)

    // Check for both brand and seller
    sponsoredElements.forEach((el) => {
      // console.log(`In sponsoredElement title is: ${JSON.stringify(el, null, 2)}`);
      let containsBrandOrSeller = false;

      // Only check brand if it's not empty
      if (brand && brand.trim()) {
        const brandCheck = el.productTitle.some((text) => {
          if (!text || text.length < 3) return false;
          const cleanText = text.toLowerCase().trim();
          const cleanBrand = brand.toLowerCase().trim();

          if (cleanText === cleanBrand) return true;

          const brandWords = cleanBrand
            .split(/[\s-]+/)
            .filter((word) => word.length > 3);
          const textWords = cleanText.split(/[\s-]+/);

          const significantMatch = brandWords.every((brandWord) =>
            textWords.some(
              (textWord) =>
                textWord === brandWord ||
                (textWord.length > 4 && textWord.includes(brandWord))
            )
          );

          if (significantMatch) {
            const fuzzyScore = fuzzysort.single(cleanBrand, cleanText)?.score;
            return fuzzyScore !== undefined && fuzzyScore > -50;
          }

          return false;
        });

        if (brandCheck) {
          containsBrandOrSeller = true;
        }
      }

      // Only check seller if it's not empty and we haven't already found a brand match
      if (!containsBrandOrSeller && sellerName && sellerName.trim()) {
        const sellerCheck = el.productTitle.some((text) => {
          if (!text || text.length < 3) return false;
          const cleanText = text.toLowerCase().trim();
          const cleanSeller = sellerName.toLowerCase().trim();

          if (cleanText === cleanSeller) return true;

          const sellerWords = cleanSeller
            .split(/[\s-]+/)
            .filter((word) => word.length > 3);
          const textWords = cleanText.split(/[\s-]+/);

          const significantMatch = sellerWords.every((sellerWord) =>
            textWords.some(
              (textWord) =>
                textWord === sellerWord ||
                (textWord.length > 4 && textWord.includes(sellerWord))
            )
          );

          if (significantMatch) {
            const fuzzyScore = fuzzysort.single(cleanSeller, cleanText)?.score;
            return fuzzyScore !== undefined && fuzzyScore > -50;
          }

          return false;
        });

        if (sellerCheck) {
          containsBrandOrSeller = true;
        }
      }
      // console.log({
      //   titles: el.productTitle,
      //   sellerName: sellerName,
      //   brand: brand,
      //   matches: containsBrandOrSeller,
      // });

      el.containsBrandOrSeller = containsBrandOrSeller;
    });

    // Draw individual boxes for non-brand sponsored elements
    const nonBrandSponsored = sponsoredElements.filter(
      (el) => !el.containsBrandOrSeller
    );

    for (const element of nonBrandSponsored) {
      // console.log({
      //   type: "nonBrandSponsored",
      //   titles: element.productTitle,
      //   sellerName: sellerName,
      //   brand: brand,
      //   matches: element.containsBrandOrSeller,
      // });
      await page.evaluate((el) => {
        const div = document.createElement("div");
        div.style.position = "absolute";
        div.style.left = `${el.x}px`;
        div.style.top = `${el.y}px`;
        div.style.width = `${el.width}px`;
        div.style.height = `${el.height}px`;
        div.style.border = "2px solid red";
        div.style.boxSizing = "border-box";
        div.style.pointerEvents = "none";
        div.style.zIndex = "9999";
        document.body.appendChild(div);
      }, element);
    }

    // Take screenshots
    const image_path = `${file_name}_search_${asin}.png`;
    const screenshotWithHeader = await page.screenshot({
      clip: {
        x: 0,
        y: 0,
        width: await page.evaluate(() => document.documentElement.clientWidth),
        height: 1500,
      },
      encoding: "binary",
    });
    await uploadImage(screenshotWithHeader, image_path, IMAGE_FOLDER);

    const full_image_path = `${file_name}_${asin}_search_full_page_result.png`;
    const fullPageScreenshot = await page.screenshot({
      fullPage: true,
      encoding: "binary",
    });
    await uploadImage(fullPageScreenshot, full_image_path, IMAGE_FOLDER);
    //   console.log({
    //     count: sponsoredElements.length,
    //     hasSponsored: sponsoredElements.length > 0,
    //     isSponsoredInFirstRow: nonBrandSponsored.some((el) => el.y <= 1300),
    //     sponsoredNonBrandCount: nonBrandSponsored.length,
    //     totalSponsoredElements: sponsoredElements,
    //     brand,
    //     searchTerm,
    //     images: {
    //       groupScreenshotsWithHeader: getS3Url(image_path, IMAGE_FOLDER),
    //       fullPage: getS3Url(full_image_path, IMAGE_FOLDER),
    //     },
    //   });
    return {
      count: sponsoredElements.length,
      hasSponsored: sponsoredElements.length > 0,
      isProspectSponsored: sponsoredElements.some((el) => el.y <= 1500 && el.containsBrandOrSeller == true),
      isSponsoredInFirstRow: nonBrandSponsored.some((el) => el.y <= 1500),
      sponsoredNonBrandCount: nonBrandSponsored.length,
      totalSponsoredElements: sponsoredElements,
      brand,
      searchTerm,
      images: {
        groupScreenshotsWithHeader: getS3Url(image_path, IMAGE_FOLDER),
        fullPage: getS3Url(full_image_path, IMAGE_FOLDER),
      },
    };
  } catch (error) {
    console.error("An error occurred:", error);
    throw error;
  } finally {
    await closePages(browser);
  }
}

module.exports = getSponsoredListing;

// Example usage and CSV functions remain the same

async function Example() {
  try {
    const asin = "test001_BrandedKeyword_us_pawspik";
    const sellerName = "pawspik";
    const brand = "pawspik";
    const company_id = 20;
    const TARGET_URL = "https://www.amazon.com";
    const searchTerm = "pawspik";
    const file_name = "Keyword search";
    const store_front_url = "https://www.amazon.com/sp?seller=A1IA5TPKC2FRXK";
    const response = await getSponsoredListing(
      asin,
      sellerName,
      brand,
      company_id,
      TARGET_URL,
      searchTerm,
      file_name
    );
    console.log({ response });
    await saveToCSV(response, file_name);
  } catch (error) {
    console.error("An error occurred:", error);
    throw error;
  }
}

async function saveToCSV(data, fileName) {
  const csvFilePath = `./${fileName}.csv`;
  const csvData = Array.isArray(data) ? data : [data];

  try {
    let csv;
    if (fs.existsSync(csvFilePath)) {
      const existingCsv = fs.readFileSync(csvFilePath, "utf8");
      const existingRows = existingCsv.split("\n");
      csv = parse(csvData, { header: false });
      csv = existingRows.concat(csv.split("\n")).join("\n");
    } else {
      csv = parse(csvData);
    }

    fs.writeFileSync(csvFilePath, csv, "utf8");
    console.log("CSV updated successfully.");
  } catch (error) {
    console.error("Error saving to CSV:", error);
  }
}

// Example();
