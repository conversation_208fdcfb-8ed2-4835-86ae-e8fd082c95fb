const { selectors } = require("../scrapeAmazon/selectors");
const cheerio = require("cheerio");
const {getSalesEstimate} = require("../jungleScoutData/process");
const Sentry = require("@sentry/node");

async function getCompWithAboutData(htmlData, ogCompany, clientId) {
  const ogCompanyLower = (ogCompany ? ogCompany : "").toLowerCase();
  try {
    console.log("   Getting competition with About Data");
    const $ = cheerio.load(htmlData);

    const products = [];
    $(selectors.productContainer).each(async (_, container) => {
      const title = $(container)
        .find(selectors.productTitle)
        .text()
        .toLowerCase();
      // console.log(title)
      const titleCheck = title.includes(ogCompany);
      const header = $(container)
        .find(selectors.brandHeader)
        .text()
        .toLowerCase();
      // console.log(titleCheck)

      const headerCheck = header === ogCompany;
      if (!(headerCheck || titleCheck)) {
        const asin = $(container).attr("data-asin");
        // const brand = await getBrandName(asin, clientId);
        // const brandCheck = brand.toLowerCase() !== ogCompanyLower;
        // if (brandCheck) {
          const monthlySalesText = $(container)
            .find(selectors.sales)
            .first()
            .text();
          const monthlySales = monthlySalesText.includes("100+")
            ? 100
            : parseInt(monthlySalesText) || 0;

          const priceWhole = $(container)
            .find(selectors.priceWhole)
            .text()
            .trim();
          const priceFraction = $(container)
            .find(selectors.priceFraction)
            .text()
            .trim();
          const price = parseFloat(priceWhole + priceFraction) || 0;
          const rating = $(container)
            .find(selectors.numOfRatingELement)
            .first()
            .text();
          const revenue = price * (monthlySales ? monthlySales : rating);
          // console.log(revenue)

          products.push({ asin, title, revenue, price });
        }
      }
    // }
  );
    if (products.length === 0) {
      return {
        data: null,
        status: "No competitor products found from about data",
      };
    }
    // console.log({ products });
    products.sort((a, b) => b.revenue - a.revenue);
    const finalProduct = products[0];
    finalProduct.source = "From Amazon through About Data Keyword";
    // if (
    //   finalProduct.asin &&
    //   (finalProduct.price === 0 || finalProduct.revenue === 0)
    // ) {
    //   // Get revenue from jungle scout
    //   let jungleScoutData = await getSalesEstimate(finalProduct.asin, clientId);
    //   finalProduct.revenue = jungleScoutData.revenue;
    //   finalProduct.price = jungleScoutData.revenue / jungleScoutData.units;
    //   finalProduct.source = jungleScoutData.status;
    // }

    return { data: finalProduct, status: "Competitor Products ->> About Data" };
  } catch (error) {
    Sentry.captureException("Error in Getting competition with About Data:", error);
    console.error("Error in Getting competition with About Data:", error);
  }
}

module.exports = getCompWithAboutData;
