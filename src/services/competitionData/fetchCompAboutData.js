const { getHtmlByProxy } = require("../../utils/getHtmlByProxy");
const { getKeyword } = require("../scrapeGPT/request");
const getCompProductDetails = require("./getCompProductDetails");
const getCompWithAboutData = require("./getCompWithAboutData");

async function fetchCompAboutData(
  aboutData,
  company_name,
  companyId,
  usage,
  clientId,
  TARGET_URL
) {
  try {
    const searchData =
      aboutData.aboutData === "No Text Found"
        ? aboutData.homepageData
        : aboutData.aboutData;

    const keywordResponse = await getKeyword(null, searchData, clientId);
    usage[0].promptTokens += keywordResponse.response.prompt_tokens;
    usage[0].completionTokens += keywordResponse.response.completion_tokens;
    usage[0].totalTokens += keywordResponse.response.total_tokens;
    usage[0].totalRuns += 1;
    const keyword = keywordResponse.response.message
      .trim()
      .replace(/[^\w\s-]/g, "")
      .replace(/\s+/g, "+")
      .replace(/^-+|-+$/g, "");

    if (keyword === "Not+found") {
      return {
        data: JSON.stringify({ status: "Invalid Keyword" }),
        companyId: companyId,
        status: "Invalid Keyword",
        compKeyPrompt: JSON.stringify(searchData),
        searchKeyword: keyword,
        competitorRevenue: 0.0,
        competitorProductName: "",
        competitorName: "",
        competitorProductAmazonURL: "",
        prospectProductName: "",
        prospectRevenue: 0.0,
        prospectProductAmazonURL: "",
        prospectRevenueSource: "",
      };
    }

    const compSearchUrl = `${TARGET_URL}/s?k=${keyword}`;
    const compSearchPage = await getHtmlByProxy(compSearchUrl, clientId);

    const compData = await getCompWithAboutData(compSearchPage, company_name);
    console.log("FILTERED COMP DATA From About:", compData);
    if (compData.data) {
      let competitorRevenue = 0;
      let competitorProductName = "";
      let competitorName = "";
      let competitorProductAmazonURL = `${TARGET_URL}/dp/${compData.data.asin}`;
      const productPage = await getHtmlByProxy(
        competitorProductAmazonURL,
        clientId
      );
      const productDetails = getCompProductDetails(productPage);
      competitorName = productDetails.brand;
      competitorProductName = productDetails.title;
      competitorRevenue = compData.data.revenue;

      return {
        data: JSON.stringify(compData.data),
        companyId: companyId,
        status: compData.status,
        compKeyPrompt: JSON.stringify(searchData),
        searchKeyword: keyword,
        competitorRevenue: competitorRevenue,
        competitorProductName: competitorProductName,
        competitorName: competitorName,
        competitorProductAmazonURL: competitorProductAmazonURL,
        prospectProductName: "",
        prospectRevenue: 0.0,
        prospectProductAmazonURL: "",
        prospectRevenueSource: "",
      };
    }

    return {
      data: null,
      companyId: companyId,
      status: compData.status,
      compKeyPrompt: JSON.stringify(searchData),
      searchKeyword: keyword,
      competitorRevenue: "",
      competitorProductName: "",
      competitorName: "",
      competitorProductAmazonURL: "",
      prospectProductName: "",
      prospectRevenue: 0.0,
      prospectProductAmazonURL: "",
      prospectRevenueSource: "",
    };
  } catch (e) {
    console.log("Error Stack:", e.stack);
    console.log("Error in Fetching Comp Data from about data" + e);
  }
}

module.exports = fetchCompAboutData;
