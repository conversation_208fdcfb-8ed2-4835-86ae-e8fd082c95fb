const { getHtmlByProxy } = require("../../utils/getHtmlByProxy");
const cheerio = require("cheerio");
const selectors = require("../scrapeAmazon/selectors");

async function getBrandName(asin, clientId,TARGET_URL) {
  //takes product asin and return the brand;
  const url = `${TARGET_URL}/dp/${asin}`;
  const htmlData = await getHtmlByProxy(url, clientId);
  const $ = cheerio.load(htmlData);
  let brand = "";
  brand = $(selectors.brandName)
    .text()
    .replace("Brand", "")
    .trim()
    .toLowerCase();

  if (brand == "") {
    const storeName = $("#bylineInfo")
      .text()
      .replace("Visit the", "")
      .replace("Store", "")
      .trim();
    brand = storeName;
  }
  if (brand == "") {
    const manufacturer = $('span:contains("Manufacturer")')
      .next("span")
      .text()
      .trim();
    brand = manufacturer.toLowerCase();
  }
  // console.log({brand})
  return brand;
}

module.exports = getBrandName;

// const asin = "B0CDNQ62ML";
// getBrandName(asin);
