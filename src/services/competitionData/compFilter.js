function compFilter({ og<PERSON>rod<PERSON>rice, ogProdRevenue, productList }) {
  if (!Array.isArray(productList)) {
    console.error("Error: productList is not an array");
    return {
      data: null,
      status: "No competitor products found from amazon data",
    };
  }

  const minRevenue = ogProdRevenue * 1.5;
  const maxRevenue = ogProdRevenue * 7;

  // Revenue Filter
  const filteredProducts = productList.filter((product) => {
    return (
      product.revenue >= minRevenue && product.revenue <= maxRevenue
    );
  });

  // Check if there are no filtered products
  if (filteredProducts.length === 0) {
    console.error(
      "No competitor products found within the specified revenue range"
    );
    return { data: null, status: "Competitor products out of range" };
  }

  // Sort filtered products by revenue in descending order
  filteredProducts.sort((a, b) => b.revenue - a.revenue);

  // Find the first product with revenue greater than ogProdRevenue
  const competitorProduct = filteredProducts.find(
    (product) => product.revenue > ogProdRevenue
  );
  // console.log("competitor product from the compFilter:", competitorProduct);
  return { data: competitorProduct, status: "Success" };
}

function getMultipliers(price) {
  let minMultiplier, maxMultiplier;

  if (price >= 1 && price <= 25) {
    minMultiplier = 0.4;
    maxMultiplier = 3;
  } else if (price > 25 && price <= 50) {
    minMultiplier = 0.5;
    maxMultiplier = 1.8;
  } else if (price > 50 && price <= 100) {
    minMultiplier = 0.6;
    maxMultiplier = 1.5;
  } else if (price > 100 && price <= 500) {
    minMultiplier = 0.6;
    maxMultiplier = 1.8;
  } else if (price > 500) {
    minMultiplier = 0.5;
    maxMultiplier = 1.8;
  }

  return { minMultiplier, maxMultiplier };
}

module.exports = { compFilter, getMultipliers}
