const { selectors } = require("../scrapeAmazon/selectors");
const cheerio = require("cheerio");
const { compFilter } = require("./compFilter");
const { getSalesEstimate } = require("../jungleScoutData/process");
const getBrandName = require("./getBrandName");
const Sentry = require("@sentry/node");
const { getHtmlByProxy } = require("../../utils/getHtmlByProxy");

async function getCompWithAmazonData({
  htmlData,
  ogPrice,
  ogCompany,
  ogRevenue,
  ogAsin,
  clientId,
  TARGET_URL,
}) {
  const ogCompanyLower = (ogCompany ? ogCompany : "").toLowerCase();
  try {
    console.log("Getting competition with Amazon Data");
    const $ = cheerio.load(htmlData);
    // console.log({ htmlData, ogPrice, ogRevenue, ogCompany });
    const products = [];
    // console.log("SELECTOR: ", selectors.productContainer)
    $(selectors.productContainer).each(async (_, container) => {
      // console.log("INSIDE CONTAINER");
      const title = $(container)
        .find(selectors.productTitle)
        .text()
        .toLowerCase();
      const ogCompanyList = ogCompanyLower.split(" ");
      // const titleList = (title || "").split(" ");
      // const titleCheck = titleList.some((word) => ogCompanyList.includes(word));
      const header = $(container)
        .find(selectors.brandHeader)
        .text()
        .toLowerCase();
      const headerCheck = header === ogCompanyLower;
      // console.log({headerCheck,titleCheck})

      if (!headerCheck) {
        const asin = $(container).attr("data-asin");
        // const brand = await getBrandName(asin, clientId,TARGET_URL);
        // const brandCheck = brand.toLowerCase() !== ogCompanyLower;
        // console.log({asin,ogAsin})
        if (asin != ogAsin) {
          const monthlySalesText = $(container)
            .find(selectors.sales)
            .first()
            .text();
          let monthlySales = 0;
          if (monthlySalesText !== "N/A") {
            const numberMatch = monthlySalesText.match(/(\d+(\.\d+)?)([K]?)\+/);
            if (numberMatch) {
              let number = parseFloat(numberMatch[1]);
              const unit = numberMatch[3];
              if (unit === "K") {
                number *= 1000;
              }
              monthlySales =
                Math.floor(number) + Math.floor(Math.random() * 9) + 1;
            }
          }
          const priceWhole = $(container)
            .find(selectors.priceWhole)
            .text()
            .trim();
          const priceFraction = $(container)
            .find(selectors.priceFraction)
            .text()
            .trim();
          const price = parseFloat(priceWhole + priceFraction) || 0;
          const rating = $(container)
            .find(selectors.numOfRatingELement)
            .first()
            .text();
          const image = $(container).find(selectors.image).attr('src')
          const revenue = price * (monthlySales ? monthlySales : rating);
          // console.log("DATA:",{title,asin,revenue,price})
          products.push({ asin, title, revenue, price, image });
        }
      }
    });
    // console.log("COMP PRODUCTS:",products)

    let filteredComp = compFilter({
      ogProdPrice: ogPrice,
      productList: products,
      ogProdRevenue: ogRevenue,
    });
    let finalProduct;
    if (!filteredComp.data) {
      console.log("RETURN HIGHEST REVENUE PRODUCT");
      finalProduct = products.reduce((maxProduct, product) => {
        return product.revenue > (maxProduct.revenue || 0)
          ? product
          : maxProduct;
      }, {});
    } else {
      finalProduct = filteredComp.data;
    }
    // console.log("Filtered Comp Data:", finalProduct);
    if (finalProduct) {
      finalProduct.source = "From Amazon through Amazon Data";
      // if (finalProduct.asin && finalProduct.price === 0) {
      //   let jungleScoutData = await getSalesEstimate(
      //     finalProduct.asin,
      //     clientId
      //   );
      //   finalProduct.revenue = jungleScoutData.revenue;
      //   finalProduct.price = jungleScoutData.revenue / jungleScoutData.units;
      //   finalProduct.source = jungleScoutData.status;
      // }
    }
    // console.log("FINAL PRIDUCT FROM AMAZON COMP:", finalProduct);
    const revenueDifference = finalProduct.revenue - ogRevenue;
    if (revenueDifference < 0) {
      return {
        data: null,
        status: "No Product found with more revenue than Prospect",
      };
    }
    return { data: finalProduct, status: filteredComp.status };
  } catch (error) {
    Sentry.captureException(
      "Error in Getting Competition with Amazon Data:",
      error
    );
    console.log(
      "Error getting competition product data for:",
      ogCompany + error
    );
    return {
      data: null,
      status: "Failed fetching competition with Amazon Data",
    };
  }
}
module.exports = getCompWithAmazonData;

async function Example() {
  try {
    const searchKeyword =
      "small round wall shelves for living room decor";
    const url = `https://www.amazon.com/s?k=${searchKeyword}`;
    const htmlData = await getHtmlByProxy(url, 1, undefined, undefined);
    const ogPrice = 37
    const ogCompany = "oakrain";
    const ogRevenue = 4000
    const ogAsin = "B0C7KRMXN1";
    const clientId = 1;
    const data = await getCompWithAmazonData({
      htmlData,
      ogPrice,
      ogCompany,
      ogRevenue,
      ogAsin,
      clientId,
    });
    console.log(data)


  } catch (error) {
    
  }
};

// Example();