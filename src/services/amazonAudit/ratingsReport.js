function getRatingsReport(data, report) {
  if (!data.productData || !data.productData[0].rating) {
    return;
  }
  const rating = data.productData[0].rating.rating;
  const ratingComesAs = data.productData[0].rating.ratingComesAs;
  const lowStarReviews = data.productData[0].review.reviewPerStar
    .filter((review) => "1starReview" in review || "2starReview" in review)
    .reduce((acc, review) => {
      const [_, value] = Object.entries(review)[0];
      acc += value;
      return acc;
    }, 0);

  // console.log("Ratings: ", data.productData[0].rating);
  if (rating < 4) {
    let Improvements = [
      `On the search page your ratings are shown as ${ratingComesAs} stars, making it very hard to compete against competition with higher ratings.`,
    ];
    if (lowStarReviews && lowStarReviews > 0) {
      Improvements.push(
        `You have ${lowStarReviews} of 1* & 2* reviews, you should get as many of them removed as possible.`,
        `Then work on increasing the number of good reviews by fixing the listing & product issues. You can do that by running a review analysis.`
      );
    } else {
      Improvements.push(
        `Work on increasing the number of good reviews by fixing the listing & product issues. You can do that by running a review analysis.`
      );
    }
    report.push({
      DATA_POINT: "Ratings",
      PRIORITY: "High",
      Logic: "below 4",
      PAIN_POINT: `You have ${rating} star rating which is low`,
      Improvements: Improvements,
      Benefits: ["CVR ↑", "CTR ↑", "Algorithm boost"],
    });
  } else if (rating < 4.3) {
    let Improvements = [
      `On the search page your ratings are shown as 4 stars, making it impossible to rank #1. Once you get that number to 4.3 it will show up 4.5 stars.`,
    ];
    if (lowStarReviews && lowStarReviews > 0) {
      Improvements.push(
        `You have ${lowStarReviews}, 1* & 2* reviews, you should get as many of them removed as possible.`,
        `Work on increasing the number of good reviews by fixing the listing & product issues. You can do that by running a review analysis.`
      );
    } else {
      Improvements.push(
        `You should work on increasing the number of good reviews by fixing the listing & product issues. You can do that by running a review analysis.`
      );
    }
    report.push({
      DATA_POINT: "Ratings",
      PRIORITY: "High",
      Logic: "4 - 4.3",
      PAIN_POINT: `You have ${rating} star rating which is decent but can be improved quickly to 4.5 stars`,
      Improvements: Improvements,
      Benefits: ["CVR ↑", "CTR ↑", "Algorithm boost"],
    });
  } else if (rating < 4.5) {
    let Improvements = [
      `On the search page your ratings are shown as 4 stars, making it impossible to rank #1. Your next aim should be 4.3 stars as it will show up 4.5 stars.`,
    ];
    if (lowStarReviews && lowStarReviews > 0) {
      Improvements.push(
        `To get this up, you should get as many 1* & 2* star reviews removed as possible, I see currently you have ${lowStarReviews}.`,
        `Work on increasing the number of good reviews by fixing the listing & product issues. You can do that by running a review analysis.`
      );
    } else {
      Improvements.push(
        `You should work on increasing the number of good reviews by fixing the listing & product issues. You can do that by running a review analysis.`
      );
    }
    report.push({
      DATA_POINT: "Ratings",
      PRIORITY: "High",
      Logic: "4.3 - 4.5",
      PAIN_POINT: `You have a ${rating} star rating which is good but this is the threshold to be downgraded to a 4-star rating`, // 4.3 wali me trigger honi chaiye
      Improvements: Improvements,
      Benefits: ["CVR ↑", "CTR ↑", "Algorithm boost"],
    });
  } else if (rating < 4.7) {
    let Improvements = [
      `On the search page your ratings are shown as 4.5 stars, making it impossible to rank #1. Once you get that number to 4.7 it will show up 5 stars.`,
    ];
    if (lowStarReviews && lowStarReviews > 0) {
      Improvements.push(
        `You have ${lowStarReviews}, 1* & 2* reviews, you should get as many of them removed as possible.`,
        `Work on increasing the number of good reviews by fixing the listing & product issues. You can do that by running a review analysis.`
      );
    } else {
      Improvements.push(
        `You should work on increasing the number of good reviews by fixing the listing & product issues. You can do that by running a review analysis.`
      );
    }
    report.push({
      DATA_POINT: "Ratings",
      PRIORITY: "High",
      Logic: "4.5 - 4.7",
      PAIN_POINT: `You have ${rating} star rating which is good but can be improved further to 5 stars, making you #1 your category.`,
      Improvements: Improvements,
      Benefits: ["CVR ↑", "CTR ↑", "Algorithm boost"],
    });
  } else {
    report.push({
      DATA_POINT: "Ratings",
      PRIORITY: "High",
      Logic: "4.7+",
      PAIN_POINT: `Great job! Your ratings are perfect just make sure they don't fall below 4.7 stars.`,
      Improvements: [
        `If they fall below 4.7 stars your ratings will show up as 4.5 stars on the Amazon search page, greatly affecting the CVR & CTR.`,
      ],
      Benefits: ["CVR ↑", "CTR ↑", "Algorithm boost"],
    });
  }
}

module.exports = getRatingsReport;
