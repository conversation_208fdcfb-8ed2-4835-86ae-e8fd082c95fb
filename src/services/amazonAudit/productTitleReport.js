const { completionFactory } = require("../../services/scrapeGPT/factory");
async function getProductTitleReport(data, report, clientId) {
  if (!data.productData || !data.productData[0].productTitle ) {
    return;
  }
  // console.log("Product Data: ", data.productData[0]);

  const titleLength = data.productData[0].productTitle.numOfChars;
  let productCategory = data.productData[0].categoryAndRank;
  // Adding selected caseStudies
  if (Array.isArray(productCategory) && productCategory.length > 0) {
    const element = productCategory[0];
    if (element && element.category) {
      productCategory = element.category;
    } else {
      productCategory = element;
    }
  } else {
    productCategory = "N/A";
  }

  if (productCategory === "N/A") {
    productCategory = data.productData[0].productTitle;
  }
  const categoryResponse = await completionFactory(
    "bsrCategoryMatch",
    productCategory,
    clientId
  );
  // console.log("Category  Response: ", categoryResponse);
  const category = categoryResponse.message;

  console.log("Category for the Title: ", category.trim());
  const titleLengthGuidelines = {
    "Automotive & Powersports (Parts & Accessories)": {
      bad: [0, 35],
      average: [35, 45],
      good: [45, Infinity],
      max: 50,
    },
    "Automotive & Powersports (Tires & Wheels)": {
      bad: [0, 35],
      average: [35, 45],
      good: [45, Infinity],
      max: 50,
    },
    Baby: {
      bad: [0, 35],
      average: [35, 45],
      good: [45, Infinity],
      max: 50,
    },
    Beauty: {
      bad: [0, 35],
      average: [35, 45],
      good: [45, Infinity],
      max: 50,
    },
    "Clothing & Accessories": {
      bad: [0, 50],
      average: [50, 70],
      good: [70, Infinity],
      max: 80,
    },
    "Consumer Electronics": {
      bad: [0, 110],
      average: [110, 130],
      good: [130, Infinity],
      max: 150,
    },
    "Entertainment Collectibles": {
      bad: [0, 35],
      average: [35, 45],
      good: [45, Infinity],
      max: 50,
    },
    "Eyewear (See Clothing & Accessories)": {
      bad: [0, 35],
      average: [35, 45],
      good: [45, Infinity],
      max: 50,
    },
    "Fine Art": {
      bad: [0, 150],
      average: [150, 180],
      good: [180, Infinity],
      max: 200,
    },
    "Gift Cards": {
      bad: [0, 50],
      average: [50, 70],
      good: [70, Infinity],
      max: 80,
    },
    "Grocery & Gourmet Food": {
      bad: [0, 35],
      average: [35, 45],
      good: [45, Infinity],
      max: 50,
    },
    "Health & Personal Care": {
      bad: [0, 35],
      average: [35, 45],
      good: [45, Infinity],
      max: 50,
    },
    "Home, Home Decor, Kitchen & Garden Furniture": {
      bad: [0, 150],
      average: [150, 180],
      good: [180, Infinity],
      max: 200,
    },
    "Industrial & Scientific": {
      bad: [0, 110],
      average: [110, 130],
      good: [130, Infinity],
      max: 150,
    },
    Jewelry: {
      bad: [0, 45],
      average: [45, 65],
      good: [65, Infinity],
      max: 100,
    },
    Lighting: {
      bad: [0, 35],
      average: [35, 45],
      good: [45, Infinity],
      max: 50,
    },
    "Luggage & Travel Accessories": {
      bad: [0, 50],
      average: [50, 70],
      good: [70, Infinity],
      max: 80,
    },
    "Motorcycles & ATVs": {
      bad: [0, 35],
      average: [35, 45],
      good: [45, Infinity],
      max: 50,
    },
    "Musical Instruments": {
      bad: [0, 110],
      average: [110, 130],
      good: [130, Infinity],
      max: 150,
    },
    "Office Products": {
      bad: [0, 110],
      average: [110, 130],
      good: [130, Infinity],
      max: 150,
    },
    Outdoors: {
      bad: [0, 35],
      average: [35, 45],
      good: [45, Infinity],
      max: 50,
    },
    "Pet Supplies": {
      bad: [0, 150],
      average: [150, 180],
      good: [180, Infinity],
      max: 200,
    },
    "Shoes, Handbags & Sunglasses": {
      bad: [0, 35],
      average: [35, 45],
      good: [45, Infinity],
      max: 50,
    },
    Sports: {
      bad: [0, 35],
      average: [35, 45],
      good: [45, Infinity],
      max: 50,
    },
    "Tools & Home Improvement": {
      bad: [0, 35],
      average: [35, 45],
      good: [45, Infinity],
      max: 50,
    },
    "Toys & Games": {
      bad: [0, 150],
      average: [150, 180],
      good: [180, Infinity],
      max: 200,
    },
    Watches: {
      bad: [0, 5],
      average: [5, 10],
      good: [10, Infinity],
      max: 10,
    },
    Default: {
      bad: [0, 150],
      average: [150, 180],
      good: [180, Infinity],
      max: [Infinity],
    },
  };
  // assign default for titleLength greater than 100
  const guidelines =
    titleLength > 100
      ? titleLengthGuidelines["Default"]
      : titleLengthGuidelines[category];
  // console.log({ guidelines, titleLength });
  if (!guidelines) {
    if (titleLength <= 150) {
      report.push({
        DATA_POINT: "Product Title",
        PRIORITY: "High",
        Logic: "0-150",
        PAIN_POINT:
          titleLength > 0
            ? `Your title character length is ${titleLength} which is causing you to miss a lot of traffic opportunities.`
            : "You do not have a product title which is causing you to miss a lot of traffic opportunities.",
        Improvements: [
          `Fully squeeze the title character length for that SEO juice.`,
          `Add lots of relevant & highly searched keywords.`,
          `Move the main keyword to the beginning.`,
        ],
        Benefits: [
          "CVR ↑",
          "CTR ↑",
          "Visibility & SEO ↑",
          "Organic Rankings ↑",
          "Indexing",
        ],
      });
    } else if (titleLength <= 180) {
      report.push({
        DATA_POINT: "Product Title",
        PRIORITY: "Medium",
        Logic: "150-180",
        PAIN_POINT: `Your title character length is ${titleLength} which is causing you to miss some traffic opportunities.`,
        Improvements: [
          `Fully squeeze the title character length for that SEO juice by adding relevant & high search volume keywords.`,
          `Move the main keywords within the first 80 characters.`,
        ],
        Benefits: [
          "CVR ↑",
          "CTR ↑",
          "Visibility & SEO ↑",
          "Organic Rankings ↑",
        ],
      });
    }
  } else {
    if (titleLength <= guidelines.bad[1]) {
      report.push({
        DATA_POINT: "Product Title",
        PRIORITY: "High",
        Logic: JSON.stringify(guidelines.bad),
        PAIN_POINT: `Your title character length is ${titleLength} which is causing you to miss a lot of traffic opportunities.`,
        Improvements: [
          `Fully squeeze the title character length for that SEO juice by adding relevant & high search volume keywords.`,
          `Move the main keywords within the first 80 characters.`,
        ],
        Benefits: [
          "CVR ↑",
          "CTR ↑",
          "Visibility & SEO ↑",
          "Organic Rankings ↑",
          "Indexing",
        ],
      });
    } else if (titleLength <= guidelines.average[1]) {
      report.push({
        DATA_POINT: "Product Title",
        PRIORITY: "Medium",
        Logic: JSON.stringify(guidelines.average),
        PAIN_POINT: `Your title character length is ${titleLength} which is causing you to miss some traffic opportunities.`,
        Improvements: [
          `Fully squeeze the title character length for that SEO juice.`,
          `Add lots of relevant & highly searched keywords.`,
          `Move the main keyword to the beginning.`,
        ],
        Benefits: [
          "CVR ↑",
          "CTR ↑",
          "Visibility & SEO ↑",
          "Organic Rankings ↑",
        ],
      });
    }
  }
}

module.exports = getProductTitleReport;
