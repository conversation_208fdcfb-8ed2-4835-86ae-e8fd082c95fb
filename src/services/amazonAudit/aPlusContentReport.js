function getAPlusContentReport(data, report) {
  if (!data.productData || !data.productData[0].AplusContent) {
    return;
  }
  // console.log("A+ Content: ", data.productData[0].AplusContent);
  const contentData = data.productData[0].AplusContent;
  const hasAPlusContent = contentData.aplusContentPresent;
  const hasAltText = contentData.allImagesHaveAltText;
  const productDescriptionLength =
    data.productData[0].textDescription.numOfChars;
  const prospectRevenue = Math.round(
    data.productData[0].price * data.productData[0].sales
  );

  const hasBrandStory = contentData.brandStory?.brandStoryPresent;
  const hasPremiumAplus = contentData.premiumAPlusPresent;

  // console.log("A+ Content: ", contentData);
  // Check if A+ content is not present
  if (!hasAPlusContent) {
    report.push({
      DATA_POINT: "A+ content",
      PRIORITY: "Urgent",
      Logic: "Not Present",
      PAIN_POINT: "There is no A+ content on the listing right now",
      Improvements: [
        `Your competitors are using A+ Content and they are getting an algorithm boost. You should also do that as it can increase sales by up to 12%` +
          (prospectRevenue > 0
            ? `, meaning an increase of $${Math.round(
                0.12 * prospectRevenue
              )} per month just for this listing.`
            : "."),
            "Also, include Alt-text in the images.",
      ],
      Benefits: ["CVR ↑", "Visibility & SEO ↑"],
    });
  }

  // Check if product description is less than 500 characters
  if (productDescriptionLength < 500) {
    report.push({
      DATA_POINT: "A+ content",
      PRIORITY: "Medium",
      Logic: "<500+ characters",
      PAIN_POINT:
        productDescriptionLength > 0
          ? `Your text description has ${productDescriptionLength} characters, this is lesser than it should be.`
          : "There is be no text description at the end of your A+ content. ",
      Improvements: [
        `You should add ` +
          (productDescriptionLength > 0 ? `more ` : ``) +
          `information about your product in the text description section` +
          (productDescriptionLength > 0
            ? ` and increase it to at least 500+ characters.`
            : "."),
      ],
      Benefits: ["CVR ↑", "Visibility & SEO ↑"],
    });
  }

  // Check if Alt Text is not there
  if (!hasAltText && hasAPlusContent) {
    report.push({
      DATA_POINT: "A+ content",
      PRIORITY: "High",
      Logic: "Alt Text is not there",
      PAIN_POINT: "There is no Alt text being used in the images",
      Improvements:
        "You should add alt-text with relevant keywords as that helps with the algorithm and boosts the listing's SEO",
      Benefits: ["Indexing", "Organic Rankings ↑"],
    });
  }

  if (hasAPlusContent && !hasPremiumAplus) {
    report.push({
      DATA_POINT: "A+ content",
      PRIORITY: "Medium",
      Logic: "A+ content is present but premium A+ content not present",
      PAIN_POINT: "Premium A+ content is not being used right now.",
      Improvements: [
        "Including Premium A+ content can increase your conversion rate, with Amazon stating it can boost sales by 20%. You should take advantage of this free feature.",
      ],
      Benefits: ["CVR ↑", "Sales ↑"],
    });
  }

  if (!hasBrandStory) {
    report.push({
      DATA_POINT: "A+ content",
      PRIORITY: "Medium",
      Logic: "Brand story not present",
      PAIN_POINT: "There is no Brand Story in the A+ content.",
      Improvements: [
        "Adding a Brand Story can help customers connect with your brand better and improve conversion rates.",
      ],
      Benefits: ["CVR ↑", "Brand Loyalty ↑"],
    });
  }
}

module.exports = getAPlusContentReport;
