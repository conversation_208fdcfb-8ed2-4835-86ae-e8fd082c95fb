function getStorefrontReport(data, report) {
  const storefrontStatus = data.store.storefront_present;

  // console.log("Storefront: ", storefrontStatus);
  if (!storefrontStatus) {
    report.push({
      DATA_POINT: "Storefront",
      PRIORITY: "High",
      Logic: "Not Present",
      PAIN_POINT: "I don't see a storefront present right now.",
      Improvements: [
        "You should make a storefront instantly. Include your best sellers at the top.",
        "Include your best sellers at the top.",
        "Get a designer to make it well and according to your brand aesthetic.",
        `Include the “follow tag” so that customers get notified about new launches.`,
      ],
      Benefits: ["CVR ↑", "Algorithm boost", "Visibility ↑"],
    });
  }
}

module.exports = getStorefrontReport;
