function getReviewReport(data, report) {
  if (!data.productData || !data.productData[0].review) {
    return;
  }
  const reviews = data.productData[0].review;
  const reviewCount = reviews.totalReviewCountInt;
  const lowStarReviews = reviews.reviewPerStar
    .filter((review) => "1starReview" in review || "2starReview" in review)
    .reduce((acc, review) => {
      const [_, value] = Object.entries(review)[0];
      acc += value;
      return acc;
    }, 0);

  // console.log("Reviews: ", reviews);
  // console.log("Low Star Reviews: ", lowStarReviews);
  if (reviewCount < 30) {
    report.push({
      DATA_POINT: "Reviews",
      PRIORITY: "High",
      Logic: "<30",
      PAIN_POINT: `You have ${reviewCount} reviews which is significantly affecting your conversions.`,
      Improvements: [
        "70+ reviews are ideal for scaling but the first you should aim for 30.",
        "The best ways to get reviews is enroll in the vine program & insert a review request card in the packaging.",
        "You can see upto 4% review rate per order with the right review funnel",
      ],
      Benefits: ["CVR ↑", "CTR ↑"],
    });
  } else if (reviewCount >= 30 && reviewCount < 70) {
    report.push({
      DATA_POINT: "Reviews",
      PRIORITY: "Medium",
      Logic: "30-70",
      PAIN_POINT: `You have ${reviewCount} reviews which is enough fuel to rev your engine but not enough to hit full speed.`,
      Improvements: [
        "The best ways to get reviews is enroll in the vine program & insert a review request card in the packaging.",
        "You can start scaling but 70+ reviews are ideal before you run ads.",
        "You can see up to 4% review rate per order with the right review funnel",
      ],
      Benefits: ["CVR ↑", "CTR ↑"],
    });
  } else if (reviewCount >= 70) {
    const nextIdealNumber = calculateNextIdealNumber(reviewCount);
    report.push({
      DATA_POINT: "Reviews",
      PRIORITY: "Low",
      Logic: "70+",
      PAIN_POINT:
        lowStarReviews > 0
          ? `Great Job! I see you have ${reviewCount} reviews but I also see ${lowStarReviews}, 1 & 2 star reviews.`
          : `Great Job! I see you have ${reviewCount} reviews but I also see some bad reviews.`,
      Improvements: [
        `You should identify Non TOS compliant reviews & get them removed. Next, aim for ${nextIdealNumber}. We usually see a huge jump at that point.`,
        "1 & 2 star reviews can completely kill the potential of a listing.",
      ],
      Benefits: ["CVR ↑", "CTR ↑"],
    });
  }
}

function calculateNextIdealNumber(currentNumber) {
  const idealNumbers = [30, 70, 100, 300, 700, 1000, 3000, 5000, 7000, 10000, 30000, 50000, 70000, 100000];

  for (let i = 0; i < idealNumbers.length; i++) {
    if (currentNumber < idealNumbers[i]) {
      return idealNumbers[i];
    }
  }

  // If the current number is greater than or equal to the largest ideal number, return the largest ideal number
  return idealNumbers[idealNumbers.length - 1];
}

module.exports = getReviewReport;
