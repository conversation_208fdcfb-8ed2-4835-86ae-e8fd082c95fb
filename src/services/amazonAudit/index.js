const getProductTitleReport = require("./productTitleReport");
const getBulletPointReport = require("./bulletPointReport");
const getImageReport = require("./imageReport");
const getAPlusContentReport = require("./aPlusContentReport");
const getVideoReport = require("./videoReport");
const getReviewReport = require("./reviewReport");
const getRatingsReport = require("./ratingsReport");
const getAdsReport = require("./adsReport");
const getStoreFrontReport = require("./storeFrontReport");
const csvtojson = require("csvtojson");
const { createObjectCsvWriter } = require("csv-writer");
const getDynamicReport = require("../dynamicAudit");
const { TooManyRequestsException } = require("@aws-sdk/client-lambda");
async function getAuditedJson(data, clientId, DYNAMIC = false) {
  // Calling all the functions to get the Audited data for the json
  let report = [];
  if (DYNAMIC) {
    report = await getDynamicReport(data, clientId);
  } else {
    report = await getStaticReport(data, clientId);
  }
  return getAggregatedReport(report);
}

async function getStaticReport(data, clientId) {
  try {
    let report = [];
    await getProductTitleReport(data, report, clientId);
    // console.log("Product Title Report Generated: ");
    getBulletPointReport(data, report);
    // console.log("Bullet Point Report Generated: ");
    getImageReport(data, report);
    // console.log("Image Report Generated: ");
    getAPlusContentReport(data, report);
    // console.log("A+ Content Report Generated: ");
    getVideoReport(data, report);
    // console.log("Video Report Generated: ");
    getReviewReport(data, report);
    // console.log("Review Report Generated: ");
    getRatingsReport(data, report);
    // console.log("Ratings Report Generated: ");
    // getAdsReport(data, report);
    // console.log("Ads Report Generated: ");
    getStoreFrontReport(data, report);

    return report;
  } catch (e) {
    console.log(e);
  }
}

function getAggregatedReport(report) {
  // Group by DATA_POINT
  const groupedReport = report.reduce((acc, item) => {
    if (!acc[item.DATA_POINT]) {
      acc[item.DATA_POINT] = [];
    }
    acc[item.DATA_POINT].push(item);
    return acc;
  }, {});

  // Sort by PRIORITY
  const priorityOrder = { Urgent: 1, High: 2, Medium: 3, Low: 4 };
  for (const key in groupedReport) {
    groupedReport[key].sort(
      (a, b) => priorityOrder[a.PRIORITY] - priorityOrder[b.PRIORITY]
    );
  }
  if (groupedReport["Bullet Points"]) {
    // console.log("Bullet Points: ", groupedReport["Bullet Points"]);
    groupedReport["Bullet Points"][0].PRIORITY =
      groupedReport["Bullet Points"].length > 2 ? "High" : "Medium";
  }

  // console.log("Grouped Report: ", groupedReport);

  // Aggregate the data
  const aggregatedReport = {};
  for (const key in groupedReport) {
    const items = groupedReport[key];
    aggregatedReport[key] = {
      PRIORITY: items[0].PRIORITY,
      Logic: items.map((item) => item.Logic),
      PAIN_POINT: items.map((item) => item.PAIN_POINT),
      Improvements: items.flatMap((item) => item.Improvements),
      Benefits: Array.from(new Set(items.flatMap((item) => item.Benefits))),
    };
  }

  // Special handling for "Bullet Points"
  if (groupedReport["Bullet Points"]) {
    aggregatedReport["Bullet Points"].PRIORITY =
      groupedReport["Bullet Points"].length > 2 ? "High" : "Medium";
  }

  return aggregatedReport;
}
async function processCSV() {
  const jsonArray = await csvtojson().fromFile("data.csv");
  for (let i = 0; i < jsonArray.length; i++) {
    const row = jsonArray[i];
    if (!row["amazonAudit"]) {
      console.log("No Amazon Audit data found for row: ", row);
      continue;
    }
    const data = JSON.parse(row["amazonAudit"]);
    const clientId = 1;
    // console.log("Data: ", data);
    const report = await getAuditedJson(data, clientId);
    console.log(`Aggregated Report for  ${data.company_name} :`, report);
    const reportJson = JSON.stringify(report);
    row["amazonAuditReport"] = reportJson;
  }

  const csvWriter = createObjectCsvWriter({
    path: "updated_data.csv",
    header: Object.keys(jsonArray[0]).map((key) => ({ id: key, title: key })),
    append: false,
  });

  await csvWriter.writeRecords(jsonArray);
  console.log("CSV file has been written");
}
// processCSV();
module.exports = getAuditedJson;
