const prisma = require('../database/prisma/getPrismaClient');
// Access Session Model

// Function to get Scraper Keys
async function getScraperKeys(clientId) {
  try {
    const config = await prisma.configurations.findFirst({
      where: {
        clientId: clientId,
      },
      select: {
        scraperApi: true,
      },
    });
    // console.log("Scraper Keys: ", config);
    return config ? config.scraperApi : await getScraperKeys(1);
  } catch (error) {
    console.error("Error Stack:", error.stack);
    console.error("Error fetching scraper keys:", error.message);
    throw new Error("Internal server error");
  }
}

// Function to get Scraping Bee Keys
async function getScrapingBeeKeys(clientId) {
  try {
    const config = await prisma.configurations.findFirst({
      where: {
        clientId: clientId,
      },
      select: {
        scrapingBeeApi: true,
      },
    });
    console.log("Scraping Bee Keys: ", config);
    return config ? config.scrapingBeeApi : await getScrapingBeeKeys(1);
  } catch (error) {
    console.error("Error Stack:", error.stack);
    console.error("Error fetching scraping bee keys:", error.message);
    throw new Error("Internal server error");
  }
}

// Function to get Jungle Keys
async function getJungleKeys(clientId) {
  try {
    const config = await prisma.configurations.findFirst({
      where: {
        clientId: clientId,
      },
      select: {
        jungleScout: true,
      },
    });
    // console.log("Jungle Keys: ", config);
    return config ? config.jungleScout : await getJungleKeys(1);
  } catch (error) {
    console.error("Error Stack:", error.stack);
    console.error("Error fetching jungle keys:", error.message);
    throw new Error("Internal server error");
  }
}

// Function to get Open API Key
async function getOpenApiKey(clientId) {
  try {
    // Print the function stack

    const config = await prisma.configurations.findFirst({
      where: {
        clientId: clientId,
      },
      select: {
        openAi: true,
      },
    });
    // console.log("Open API Key: ", config);
    return config ? config.openAi : await getOpenApiKey(1);
  } catch (error) {
    console.error("Error Stack:", error.stack);
    console.error("Error fetching Open API key:", error.message);
    throw new Error("Internal server error");
  }
}

//Function to get the Competitor Email Template
async function getCompetitorEmailTemplate(clientId) {
  try {
    const config = await prisma.configurations.findFirst({
      where: {
        clientId: clientId,
      },
      select: {
        compEmailTemplates: true,
      },
    });
    // console.log("Competitor Email Template: ", config);
    return config ? config.compEmailTemplates : await getCompetitorEmailTemplate(1);
  } catch (error) {
    console.error("Error Stack:", error.stack);
    console.error("Error fetching competitor email template:", error.message);
    throw new Error("Internal server error");
  }
}

// Function for getting prompts for the chatGPT
async function getPromptTemplates(clientId) {
  try {
    const config = await prisma.configurations.findFirst({
      where: {
        clientId: clientId,
      },
      select: {
        promptTemplates: true,
      },
    });
    // console.log("Prompt Templates: ", config);
    return config ? config.promptTemplates : await getPromptTemplates(1);
  } catch (error) {
    console.error("Error Stack:", error.stack);
    console.error("Error fetching prompt templates:", error.message);
    throw new Error("Internal server error");
  }
}


module.exports = {
  getScraperKeys,
  getScrapingBeeKeys,
  getJungleKeys,
  getOpenApiKey,
  getCompetitorEmailTemplate,
  getPromptTemplates,
};