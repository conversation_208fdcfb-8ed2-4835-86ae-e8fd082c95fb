const express = require("express");
const {
  appendDataToSheetWithRetry,
} = require("../services/replyAutomation/sheet");
const { sendBlockToSlack } = require("../utils/slack");
const prisma = require("../database/prisma/getPrismaClient");
const router = express.Router();

router.post("/api/reply-automation/:slug", async (req, res) => {
  try {
    const slug = req.params.slug;
    const restOfData = req.body;

    const user = await prisma.user.findFirst({
      where: { slug },
    });

    const master = await prisma.user.findFirst({
      where: { id: 1 },
    });
    // Validate inputs
    if (!user) {
      return res
        .status(400)
        .send({ error: "client_name is required in query parameters" });
    }

    const data = { ...restOfData };
    console.log("Received Data:", data);
    master.name = user.name;

    let sheetSuccess = false;
    let slackSuccess = false;

    // Try appending data to Google Sheet
    try {
      await appendDataToSheetWithRetry(data, master);
      sheetSuccess = true;
    } catch (error) {
      console.error("Error appending data to sheet:", error);
    }

    // Try sending Slack message
    try {
      await sendBlockToSlack(process.env.SLACK_URL, data, user.name);
      slackSuccess = true;
    } catch (error) {
      console.error("Error sending Slack message:", error);
    }

    // If both fail, return error response
    if (!sheetSuccess && !slackSuccess) {
      return res
        .status(500)
        .send("Both data appending to sheet and Slack message failed.");
    }

    res
      .status(200)
      .send(
        `Operation completed. Sheet update: ${
          sheetSuccess ? "Success" : "Failed"
        }, Slack message: ${slackSuccess ? "Success" : "Failed"}`
      );
  } catch (error) {
    console.error("Error handling request:", error);
    res.status(500).send("Internal Server Error");
  }
});

module.exports = router;
