const express = require("express");
const prisma = require("../database/prisma/getPrismaClient");

const router = express.Router();

// Add all the requestLog routes here

router.get("/api/request_logs/:limit", async (req, res) => {
  try {
    const limit = parseInt(req.params.limit);
    const requestLogs = await prisma.requestLog.findMany({
      take: limit,
      orderBy: {
        createdAt: "desc",
      },
    });
    res.json(requestLogs);
  } catch (error) {
    console.error("Error Stack:", error.stack);
    console.error("Error fetching request logs:", error.message);
    res.status(500).json({ error: "Internal server error" });
  }
});

module.exports = router;