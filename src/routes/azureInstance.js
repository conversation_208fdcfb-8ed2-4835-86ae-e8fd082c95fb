const express = require('express');
const azureInstanceController = require('../controllers/azureInstanceController');
const authMiddleware = require('../middleware/auth');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authMiddleware.verifyToken);
router.use(authMiddleware.isAdmin); // Only admin users can access these endpoints

// Get all VM instances
router.get('/api/azure/instances', azureInstanceController.listInstances);

// Start a VM instance
router.post('/api/azure/instances/:instanceId/start', azureInstanceController.startInstance);

// Stop a VM instance
router.post('/api/azure/instances/:instanceId/stop', azureInstanceController.stopInstance);

// Reboot a VM instance
router.post('/api/azure/instances/:instanceId/reboot', azureInstanceController.rebootInstance);

// Run startup script on a VM instance
router.post('/api/azure/instances/:instanceId/startup', azureInstanceController.runStartupScript);

// Alternative routes with 'id' parameter for backward compatibility
router.post('/api/azure/instances/:id/start', azureInstanceController.startInstance);
router.post('/api/azure/instances/:id/stop', azureInstanceController.stopInstance);
router.post('/api/azure/instances/:id/reboot', azureInstanceController.rebootInstance);
router.post('/api/azure/instances/:id/startup', azureInstanceController.runStartupScript);

module.exports = router;
