const express = require("express");
const { MultiProviderAI } = require("../services/ai/multiProviderAI");

const router = express.Router();

// GET /api/ai/models - Get all available AI models
router.get("/api/ai/models", async (req, res) => {
  try {
    const aiService = new MultiProviderAI();
    const models = aiService.getAvailableModels();

    res.json({
      success: true,
      data: models,
      count: models.length,
      message: "Available AI models for LEX system",
    });
  } catch (error) {
    console.error("Error fetching available models:", error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch available models",
      details: error.message,
    });
  }
});

// GET /api/ai/models/:modelId - Get specific model details
router.get("/api/ai/models/:modelId", async (req, res) => {
  try {
    const { modelId } = req.params;
    const aiService = new MultiProviderAI();
    const models = aiService.getAvailableModels();
    
    const model = models.find(m => m.id === modelId);
    
    if (!model) {
      return res.status(404).json({
        success: false,
        error: "Model not found",
        availableModels: models.map(m => m.id),
      });
    }

    res.json({
      success: true,
      data: model,
    });
  } catch (error) {
    console.error("Error fetching model details:", error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch model details",
      details: error.message,
    });
  }
});

// GET /api/ai/models/validate/:modelId - Validate if a model is supported
router.get("/api/ai/models/validate/:modelId", async (req, res) => {
  try {
    const { modelId } = req.params;
    const aiService = new MultiProviderAI();
    const models = aiService.getAvailableModels();
    
    const isSupported = models.some(m => m.id === modelId);
    const supportedModels = models.map(m => m.id);

    res.json({
      success: true,
      data: {
        modelId,
        isSupported,
        supportedModels,
      },
    });
  } catch (error) {
    console.error("Error validating model:", error);
    res.status(500).json({
      success: false,
      error: "Failed to validate model",
      details: error.message,
    });
  }
});

// GET /api/ai/models/by-provider/:provider - Get models by provider
router.get("/api/ai/models/by-provider/:provider", async (req, res) => {
  try {
    const { provider } = req.params;
    const aiService = new MultiProviderAI();
    const allModels = aiService.getAvailableModels();
    
    const models = allModels.filter(m => 
      m.provider && m.provider.toLowerCase().includes(provider.toLowerCase())
    );

    res.json({
      success: true,
      data: models,
      count: models.length,
      provider: provider,
    });
  } catch (error) {
    console.error("Error fetching models by provider:", error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch models by provider",
      details: error.message,
    });
  }
});

// GET /api/ai/models/recommended/:useCase - Get recommended models for specific use case
router.get("/api/ai/models/recommended/:useCase", async (req, res) => {
  try {
    const { useCase } = req.params;
    const aiService = new MultiProviderAI();
    const allModels = aiService.getAvailableModels();
    
    const models = allModels.filter(m => 
      m.recommended_for && m.recommended_for.some(rec => 
        rec.toLowerCase().includes(useCase.toLowerCase())
      )
    );

    res.json({
      success: true,
      data: models,
      count: models.length,
      useCase: useCase,
      message: models.length > 0 ? 
        `Found ${models.length} models recommended for "${useCase}"` : 
        `No specific recommendations found for "${useCase}". Consider using azure-gpt4o for general purposes.`,
    });
  } catch (error) {
    console.error("Error fetching recommended models:", error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch recommended models",
      details: error.message,
    });
  }
});

module.exports = router;
