const express = require("express");
const prisma = require("../database/prisma/getPrismaClient");
const { generateAuditPdf } = require("../utils/getAmazonAudit");
const { adminAuth } = require("../middlewares/jwt");
const multer = require("multer");
const upload = multer({ dest: "uploads/" });

const router = express.Router();

// Add all the Audit Report routes here
router.get("/api/get_amazon_audit_report/:slug", async (req, res) => {
  // const rqObject = await prisma.requestLog.create({
  //   data: {
  //     url: req.url,
  //     method: req.method,
  //     headers: req.headers,
  //     body: req.body,
  //   },
  // });
  const slug = req.params.slug || "";
  const campaign = await prisma.outputData.findFirst({
    where: {
      productSlug: slug,
    },
    select: {
      campaignName: true,
      email: true,
      companyName:true
    },
  });

 const campaignName = encodeURIComponent(
   campaign?.campaignName?.trim() || "DefaultCampaign"
 );


  try {
    console.log("Fetching for company:", slug);

    const ppcReport = await prisma.pPCData.findFirst({
      where: {
        slug,
      },
      orderBy: {
       updatedAt:"desc"
      },
    });
    let ppcReportToSend = {};
    if (
      ppcReport &&
      (Object.keys(ppcReport.BrandedSearch).length ||
        Object.keys(ppcReport.nonBrandedKeywordSearch).length ||
        Object.keys(ppcReport.CompetitorSearch).length ||
        Object.keys(ppcReport.ProspectPDPImage).length ||
        Object.keys(ppcReport.BrandedKeywordSearch).length)
    ) {
      ppcReportToSend = {
        BrandedSearch: {
          image: ppcReport.BrandedSearch?.images?.groupScreenshotsWithHeader,
          text: ppcReport.BrandedSearch?.text,
        },
        BrandedKeywordSearch: {
          image:
            ppcReport.BrandedKeywordSearch?.images?.groupScreenshotsWithHeader,
          text: ppcReport.BrandedKeywordSearch?.text,
        },
        nonBrandedKeywordSearch: {
          image:
            ppcReport.nonBrandedKeywordSearch?.images
              ?.groupScreenshotsWithHeader,
          text: ppcReport.nonBrandedKeywordSearch?.text,
        },
        ProspectPDPImage: {
          image: ppcReport.ProspectPDPImage?.images?.carousels,
          text: ppcReport.ProspectPDPImage?.text,
        },
        CompetitorBrandedSearch: {
          image: ppcReport.CompetitorSearch?.images?.groupScreenshotsWithHeader,
          text: ppcReport.CompetitorSearch?.text,
        },
      };
    }

    const report = await prisma.amazonAuditReport.findFirst({
      where: {
        slug: slug,
      },
      orderBy: {
        updatedAt: "desc",
      },
      select: {
        auditReport: true,
        companyId: true,
        companyName: true,
        pdfUrl: true,
        sellerEmail: true,
        finalUrl: true,
        competitorUrl: true,
        productUrl: true,
        pageImage: true,
        productImage: true,
        caseStudies: true,
        prospectDetails: true,
      },
    });
    if (report) {
      // report.finalUrl = `${report.finalUrl}&campaign=${campaignName}`;
      // report.pdfUrl = `${report.pdfUrl}&campaign=${campaignName}`;
      report.productUrl = `${report.productUrl}&utm_campaign=${campaignName}`;

      const userId = slug.split("-").pop();
      const user = await prisma.user.findUnique({
        where: { id: parseInt(userId, 10) },
        select: {
          email: true,
          name: true,
          slug: true,
          signature: true,
          logo: true,
          mbLogo: true,
          tableLogo: true,
          ctaLink: true,
          bgColor: true,
          caseStudies: true,
          testimonials: true,
          website: true,
          offerLine: true,
          ctaButtonText: true,
          caseStudiesText: true,
        },
      });

      if (user) {
        user.ctaLink = `${user.ctaLink}&utm_campaign=${campaignName}&utm_email=${campaign?.email}&utm_sellerId=${campaign?.companyName}`;

        if (user.caseStudies) {
          for (const caseStudy of Object.values(user.caseStudies)) {
            if (caseStudy.link) {
              caseStudy.link = `${caseStudy.link}&utm_campaign=${campaignName}&utm_email=${campaign?.email}&utm_sellerId=${campaign?.companyName}`;
            }
          }
        }

        if (user.testimonials) {
          if (Array.isArray(user.testimonials)) {
            for (const testimonial of user.testimonials) {
              if (testimonial.video) {
                testimonial.video = `${testimonial.video}&campaign=${campaignName}&utm_email=${campaign?.email}&utm_sellerId=${campaign?.companyName}`;
              }
            }
          } else if (typeof user.testimonials === "object") {
            for (const testimonial of Object.values(user.testimonials)) {
              if (testimonial.video) {
                testimonial.video = `${testimonial.video}&campaign=${campaignName}&utm_email=${campaign?.email}&utm_sellerId=${campaign?.companyName}`;
              }
            }
          }
        }

        report.user = user;

        if (!report.prospectDetails) {
          const outputData = await prisma.outputData.findFirst({
            where: { productSlug: slug },
            select: { prospectDetails: true },
          });
          report.prospectDetails = outputData?.prospectDetails;
        }

        const finalCaseStudies = report.caseStudies?.length
          ? report.caseStudies
          : ["3", "6", "4", "7", "9"]
              .sort(() => Math.random() - 0.5)
              .slice(0, 2);

        report.user.caseStudies = Object.fromEntries(
          Object.entries(user.caseStudies || {}).filter(([id]) =>
            finalCaseStudies.includes(id)
          )
        );

        report.ppcReport = ppcReportToSend;

        // await prisma.requestLog.update({
        //   where: { id: rqObject.id },
        //   data: {
        //     response: report,
        //     statusCode: 200,
        //     updatedAt: new Date(),
        //   },
        // });

        return res.status(200).json(report);
      } else {
        return res.status(404).json({ message: "User not found" });
      }
    } else {
      // await prisma.requestLog.update({
      //   where: { id: rqObject.id },
      //   data: {
      //     response: { error: "Report not found" },
      //     statusCode: 404,
      //     updatedAt: new Date(),
      //   },
      // });

      return res.status(404).json({ error: "Report not found" });
    }
  } catch (error) {
    console.error("Error Stack:", error.stack);
    // await prisma.requestLog.update({
    //   where: { id: rqObject.id },
    //   data: {
    //     response: { error: error.message },
    //     statusCode: 500,
    //     updatedAt: new Date(),
    //   },
    // });

    return res.status(500).json({ error: "Internal server error" });
  }
});

// Generate PDF for the given slug
router.get("/api/generate_pdf/:slug", async (req, res) => {
  console.log("REQUEST BODY:", req.body);
  let slug = req.params.slug;
  let { regenerate } = req.body;

  // const rqObject = await prisma.requestLog.create({
  //   data: {
  //     url: req.url,
  //     method: req.method,
  //     headers: req.headers,
  //     body: req.body,
  //   },
  // });

  try {
    let auditReport = await prisma.amazonAuditReport.findFirst({
      where: {
        slug: slug,
      },
    });

    if (!auditReport) {
      // await prisma.requestLog.update({
      //   where: {
      //     id: rqObject.id,
      //   },
      //   data: {
      //     response: {
      //       error: "Error Getting Amazon Audit For: No Data Found",
      //     },
      //     statusCode: 400,
      //     updatedAt: new Date(),
      //   },
      // });
      return res.status(400).json({
        error: "Error Getting Amazon Audit For: No Data Found",
      });
    }
    await generateAuditPdf(slug);

    // await prisma.requestLog.update({
    //   where: {
    //     id: rqObject.id,
    //   },
    //   data: {
    //     response: auditReport,
    //     statusCode: 200,
    //     updatedAt: new Date(),
    //   },
    // });
    auditReport = await prisma.amazonAuditReport.findFirst({
      where: {
        slug: slug,
      },
    });

    res.status(200).json(auditReport);
  } catch (error) {
    console.error("Error Getting Amazon Audit For:", error);
    console.error("Error Stack:", error.stack);
    console.error("Error Getting Amazon Audit For:", error.message);
    // await prisma.requestLog.update({
    //   where: {
    //     id: rqObject.id,
    //   },
    //   data: {
    //     response: JSON.stringify({
    //       error: "Error Getting Amazon Audit For: ---Error:" + error,
    //     }),
    //     statusCode: 500,
    //     updatedAt: new Date(),
    //   },
    // });
    res.status(500).json({
      error: "Error Getting Amazon Audit For: ---Error:" + error,
    });
  }
});

// Generate Dummy audit preview
router.get("/api/dummy_audit/:clientId", async (req, res) => {
  // Get the client id
  const clientId = req.params.clientId;
  // Get the client details
  const client = await prisma.user.findUnique({
    where: {
      id: parseInt(clientId, 10),
    },
    select: {
      email: true,
      name: true,
      slug: true,
      signature: true,
      logo: true,
      mbLogo: true,
      tableLogo: true,
      ctaLink: true,
      bgColor: true,
      caseStudies: true,
      testimonials: true,
      Configurations: true, // Include configurations within select
      offerLine: true,
      ctaButtonText: true,
      website: true,
      caseStudiesText: true,
    },
  });
  if (!client) {
    return res.status(404).send("Client not found");
  }

  const dummyReport = {
    ppcAudit: {
      BrandedSearch: {
        text: "I also searched for ibenzer saw competitors running ads on that. Imagine the customers who are ACTIVELY looking for your exact product but end up buying from the competitor because they are coming on top…",
        count: 11,
        images: {
          fullPage:
            "https://eq--assets.s3.ap-south-1.amazonaws.com/pdptest/brand_B0C7SBZVF4_search_full_page_result.png",
          groupScreenshotsWithHeader: [
            "https://eq--assets.s3.ap-south-1.amazonaws.com/pdptest/brand_search_B0C7SBZVF4_1.png",
          ],
        },
        hasSponsored: true,
        sponsoredElements: [
          {
            x: 362.728557035964,
            y: 567.275432035964,
            width: 300.558510928072,
            height: 821.011635928072,
            element: {},
            productTitle: [
              "amazon's choice: overall pick",
              "ibenzer",
              "compatible with 2024 2023 2022 macbook air 13 inch case m3 a3113 m2 a2681, hardshell case & keyboardcover & screenfilm & type-c adapter for mac air 13.6, crystal clear, at13-kk-cycl+2tc",
            ],
            containsBrand: true,
          },
          {
            x: 663.9234816601617,
            y: 569.1422316601617,
            width: 296.8249116796767,
            height: 817.2780366796767,
            element: {},
            productTitle: [
              "ibenzer",
              'compatible with new 2024 2023 macbook air 15 inch case m3 a3114 m2 a2941, hard shell case & keyboardcover & screenfilm for mac air 15.3" retina display & touch id, crystal clear, at15-cycl+2',
            ],
            containsBrand: true,
          },
          {
            x: 961.2801531727637,
            y: 567.1707781727637,
            width: 300.7678186544726,
            height: 821.2209436544726,
            element: {},
            productTitle: [
              "ibenzer",
              'compatible with new 2024 2023 macbook air 15 inch case m3 a3114 m2 a2941, hardshell case & keyboardcover & type-c adapter for mac air 15.3" retina display&touch id, crystalclear, at15-cycl+1tc',
            ],
            containsBrand: true,
          },
          {
            x: 964.3131078666646,
            y: 2836.344357866665,
            width: 294.7019092666709,
            height: 745.1550342666709,
            element: {},
            productTitle: [
              "mosiso",
              "compatible with macbook pro 13 inch case m2 2024, 2023, 2022-2016 a2338 m1 a2251 a2289 a2159 a1989 a1708 a1706, plastic hard shell&keyboard cover&screen protector&storage bag, crystal clear",
            ],
            containsBrand: false,
          },
          {
            x: 1260.203920300895,
            y: 2832.907045300895,
            width: 301.5765343982104,
            height: 752.0296593982104,
            element: {},
            productTitle: [
              "teryeefi",
              "for macbook air 13.6 inch case m3 m2 (2022-2024 release),fits model:a3113 a2681,[ultra thin] & [upgrade crystal transparent] hard shell case+ keyboard cover+ adapter, clear",
            ],
            containsBrand: false,
          },
          {
            x: 1560.212540238342,
            y: 2833.587540238342,
            width: 300.2155445233162,
            height: 750.6686695233162,
            element: {},
            productTitle: [
              "ibenzer",
              "compatible with 2024 2023 2022 macbook air 13 inch case m3 a3113 m2 a2681, hardshell case & keyboardcover & screenfilm for mac air 13.6 with touch id, crystal clear, kk-cycl+2",
            ],
            containsBrand: true,
          },
          {
            x: 365.5557645002095,
            y: 5918.555764500209,
            width: 294.904095999581,
            height: 748.357220999581,
            element: {},
            productTitle: [
              "mosiso",
              "compatible with macbook air 13 inch case 2024 2023 2022 m3 a3113 m2 a2681 touch id, plastic hard shell&keyboard cover&screen film&type c adapter for macbook air 13.6 inch case, crystal clear",
            ],
            containsBrand: false,
          },
          {
            x: 664.2594959653251,
            y: 5917.931370965325,
            width: 296.1528830693498,
            height: 749.6060080693499,
            element: {},
            productTitle: [
              "ibenzer",
              "compatible with 2024 2023 m2 macbook pro 13 inch case 2024-2016 m1 a2338 a2289 a2251 a2159 a1989 a1706 a1708, hard shell case & keyboard cover for mac pro 13, crystal clear, t13cycl+1a",
            ],
            containsBrand: true,
          },
          {
            x: 960.5271908523521,
            y: 5914.870940852352,
            width: 302.2737432952958,
            height: 755.7268682952958,
            element: {},
            productTitle: [
              "ibenzer",
              "compatible with new 2024 2023 macbook pro 16 inch case m3 a2991 m2 a2780 m1 a2485 pro max, hard shell case with keyboard cover for mac pro 16(2024-2021), crystal clear, t16x-cycl+1",
            ],
            containsBrand: true,
          },
          {
            x: 365.2988408837024,
            y: 6671.080090883703,
            width: 1492.730443232595,
            height: 342.8241932325953,
            element: {},
            productTitle: [
              "compatible with m3 m2 macbook air 15 inch case a3114 a2941, ultra-thin & anti-fingerprints case for 2024 2023 macbook air 15.3” with m3 m2 chip & retina display touch id, transparent",
            ],
            containsBrand: false,
          },
          {
            x: 365.436576847775,
            y: 6671.217826847775,
            width: 1492.45497130445,
            height: 342.5487213044501,
            element: {},
            productTitle: [
              "compatible with m3 m2 macbook air 15 inch case a3114 a2941, ultra-thin & anti-fingerprints case for 2024 2023 macbook air 15.3” with m3 m2 chip & retina display touch id, transparent",
            ],
            containsBrand: false,
          },
        ],
      },
      BrandedKeywordSearch: {
        text: "I also searched for ibenzer saw competitors running ads on that. Imagine the customers who are ACTIVELY looking for your exact product but end up buying from the competitor because they are coming on top…",
        count: 13,
        images: {
          fullPage:
            "https://eq--assets.s3.ap-south-1.amazonaws.com/pdptest/keyword_B0C7SBZVF4_search_full_page_result.png",
          groupScreenshotsWithHeader: [
            "https://eq--assets.s3.ap-south-1.amazonaws.com/pdptest/keyword_search_B0C7SBZVF4_1.png",
          ],
        },
        hasSponsored: true,
        sponsoredElements: [
          {
            x: 365.4249412694343,
            y: 616.8624412694344,
            width: 295.1657424611313,
            height: 833.6188674611313,
            element: {},
            productTitle: [
              "mosiso compatible with macbook air 13 inch case 2024 2023 2022 m3 a3113 m2 a2681 touch id, plastic hard shell&keyboard cover&screen film&type c adapter for macbook air 13.6 inch case, baby pink",
            ],
            containsBrand: false,
          },
          {
            x: 663.415165360577,
            y: 615.524540360577,
            width: 297.8415442788459,
            height: 836.294669278846,
            element: {},
            productTitle: [
              "mosiso compatible with macbook air 13 inch case 2024 2023 2022 m3 a3113 m2 a2681 touch id, plastic hard case&keyboard cover&screen film&pouch compatible with macbook air 13.6 inch case, emerald green",
            ],
            containsBrand: false,
          },
          {
            x: 960.4824622073256,
            y: 613.2637122073256,
            width: 302.3632005853487,
            height: 840.8163255853487,
            element: {},
            productTitle: [
              "eoocoo case compatible with macbook pro 14 inch 2024 2023 2022 2021 m3 m2 m1 a2918 a2992 a2779 a2442 pro max chip, hard shell cases with keyboard cover, screen protector - crystal clear",
            ],
            containsBrand: false,
          },
          {
            x: 963.796664082079,
            y: 3031.312289082079,
            width: 295.734796835842,
            height: 823.1879218358421,
            element: {},
            productTitle: [
              "coatit for macbook air 13.6 inch case 2024 m3 a3113 2022 m2 chip model a2681, for macbook air 13 m2 m3 case with keyboard skin cover and screen protector, crystal purple",
            ],
            containsBrand: false,
          },
          {
            x: 1264.126225701245,
            y: 3032.313725701244,
            width: 293.7319235975106,
            height: 821.1850485975107,
            element: {},
            productTitle: [
              "360° protective laptop sleeve for 13-inch macbook air m2/a2681 m1/a2337 2018-2022, macbook pro m2/a2686 m1/a2338 2016-2022, water-resistant case for 12.9 ipad pro 6th/5th/4th/3rd gen, black",
            ],
            containsBrand: false,
          },
          {
            x: 1560.279972549779,
            y: 3029.139347549779,
            width: 300.0806799004421,
            height: 827.5338049004421,
            element: {},
            productTitle: [
              "anban for macbook air 13 inch case 2024 2023 2022, model m3 a3113 m2 a2681 [100% ultra clear] protective hard shell case + keyboard cover + screen skin for macbook air 13.6 inch case - crystal clear",
            ],
            containsBrand: false,
          },
          {
            x: 362.8300340868821,
            y: 6864.642534086882,
            width: 300.3555568262358,
            height: 712.8086818262358,
            element: {},
            productTitle: [
              "mosiso compatible with macbook air 13 inch case 2024 2023 2022 m3 a3113 m2 a2681, anti-cracking heavy duty tpu bumper plastic hard case&keyboard skin&screen film for macbook air 13.6, emerald green",
            ],
            containsBrand: false,
          },
          {
            x: 662.4362948365259,
            y: 6864.920669836526,
            width: 299.7992853269481,
            height: 712.252410326948,
            element: {},
            productTitle: [
              "mosiso compatible with macbook pro 16 inch case 2024 2023 2022 2021 release m3 a2991 m2 a2780 m1 a2485 pro max, plastic hard shell case&keyboard cover&screen protector&storage bag, crystal clear",
            ],
            containsBrand: false,
          },
          {
            x: 963.6004636764884,
            y: 6866.756713676488,
            width: 296.1271976470231,
            height: 708.5803226470231,
            element: {},
            productTitle: [
              'icasso compatible with macbook pro 13 inch case 2024 2023-2016 release a2338m1/a2159/a1989/a1706/a1708, plastic hard shell case with keyboard cover for macbook pro 13",pink abstract wave',
            ],
            containsBrand: false,
          },
          {
            x: 361.6644390505158,
            y: 3854.617564050516,
            width: 1499.999246898969,
            height: 350.0929968989685,
            element: {},
            productTitle: [
              "meegoodo for m3 macbook air 15 inch case 2024 2023, clear cover fit model a3114 a2941 m2,flexible plastic case for 15.3-inch laptop,hard shell with tpu keyboard & webcam cover & otg adapter, deep gray",
            ],
            containsBrand: false,
          },
          {
            x: 361.3449973207303,
            y: 7574.938747320731,
            width: 1500.638130358539,
            height: 350.7318803585393,
            element: {},
            productTitle: [
              "meegoodo for m3 macbook air m2 case 2024 2023 with m3 m2, clear case fit model a3114 a2941 for macbook air 15 inch, laptop hard shell with keyboard&webcam cover&otg adapter & screen protector, cacti",
            ],
            containsBrand: false,
          },
          {
            x: 363.681931975756,
            y: 3856.635056975756,
            width: 1495.964261048488,
            height: 346.0580110484879,
            element: {},
            productTitle: [
              "meegoodo for m3 macbook air 15 inch case 2024 2023, clear cover fit model a3114 a2941 m2,flexible plastic case for 15.3-inch laptop,hard shell with tpu keyboard & webcam cover & otg adapter, deep gray",
            ],
            containsBrand: false,
          },
          {
            x: 361.8567298825225,
            y: 7575.450479882523,
            width: 1499.614665234955,
            height: 349.7084152349551,
            element: {},
            productTitle: [
              "meegoodo for m3 macbook air m2 case 2024 2023 with m3 m2, clear case fit model a3114 a2941 for macbook air 15 inch, laptop hard shell with keyboard&webcam cover&otg adapter & screen protector, cacti",
            ],
            containsBrand: false,
          },
        ],
      },
      ProspectPDPImage: {
        text: "Product defense on your ASIN is not very strong, 0 spots have been captured by your different competitors.",
        count: 0,
        images: {
          fullPage:
            "https://eq--assets.s3.ap-south-1.amazonaws.com/pdptest/pdp_B0C7SBZVF4_ad_full_page_result.png",
          carousels: [],
        },
        hasSponsored: false,
        sponsoredElements: [],
      },
      CompetitorBrandedSearch: {
        imageUrl: "",
        text: "You`re not targeting the {{competitor brand name humaised}} while other competitors are at the top and stealing their traffic.",
      },
    },
    amazonAudit: {
      Ratings: {
        Logic: ["4 - 4.3"],
        Benefits: ["CVR ↑", "CTR ↑", "Algorithm boost"],
        PRIORITY: "High",
        PAIN_POINT: [
          "You have 4.2 star rating which is decent but can be improved further.",
        ],
        Improvements: [
          "On the search page your ratings are shown as 4 stars, making it impossible to rank #1. Once you get that number to 4.3 it will show up 4.5 stars.",
          "You have 36, 1* & 2* reviews, you should get as many of them removed as possible.",
          "Then work on increasing the number of good reviews by fixing the listing & product issues. You can do that by running a review analysis.",
        ],
      },
      Reviews: {
        Logic: ["70+"],
        Benefits: ["CVR ↑", "CTR ↑"],
        PRIORITY: "Low",
        PAIN_POINT: [
          "Great Job! I see you have 277 reviews but I also see 36, 1 & 2 star reviews.",
        ],
        Improvements: [
          "You should identify Non TOS compliant reviews & get them removed.",
          "1 & 2 star reviews can completely kill the potential of a listing.",
          "Next, aim for 300. We usually see a huge jump at that point.",
        ],
      },
      "A+ content": {
        Logic: ["<500+ characters"],
        Benefits: ["CVR ↑", "Visibility & SEO ↑"],
        PRIORITY: "Medium",
        PAIN_POINT: ["There is no product description"],
        Improvements: [
          "You should add information about your product in the text description section.",
        ],
      },
      "Bullet Points": {
        Logic: [
          "<1000 characters total",
          "Bullet points have less than 150 Chars",
        ],
        Benefits: ["Visibility & SEO ↑", "Organic Rankings ↑", "CVR ↑"],
        PRIORITY: "Medium",
        PAIN_POINT: [
          "Your bullet point's total character length is 664 which is less than it should be.",
          "2, 3 bullet points have less than 150 characters.",
        ],
        Improvements: [
          "Add more information about the product & its features in the bullet points.",
          "Have at least 1000 characters in total to maximize SEO.",
          "You should increase the length of each bullet point. Ideal length is 150-250 characters.Bullet Point 2 has 97 characters. ",
        ],
      },
    },
  };

  if (dummyReport.user.Configurations.length > 0) {
    dummyReport.user.Configurations = dummyReport.user.Configurations[0];
  } else {
    dummyReport.user.Configurations = {};
  }

  res.json(dummyReport);
});

module.exports = router;
