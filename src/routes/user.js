const express = require("express");
const bcrypt = require("bcrypt");
const jwt = require("jsonwebtoken");
const prisma = require("../database/prisma/getPrismaClient"); // Adjust the path as needed
const Sentry = require("@sentry/node");
const Joi = require("joi");

const router = express.Router();

const JWT_SECRET = process.env.JWT_SECRET;
const { userAuth, adminAuth } = require("../middlewares/jwt");
const createAndUpdateUTMUrl = require("../utils/utmHelpers/createAndUpdateUTMUrl");

// Define the Joi schema for user data
const userSchema = Joi.object({
  name: Joi.string().default(""),
  signature: Joi.string().default(""),
  logo: Joi.string().default(""),
  mbLogo: Joi.string().default(""),
  tableLogo: Joi.string().default(""),
  ctaLink: Joi.string().default(""),
  bgColor: Joi.string().default(""),
  googleSheetURL: Joi.string().default(""),
  offerLine: Joi.string().default(""),
  ctaButtonText: Joi.string().default(""),
  caseStudiesText: Joi.string().default("Read full case study here"),
  website: Joi.string().default(""),
  caseStudies: Joi.object()
    .pattern(
      Joi.string(),
      Joi.object({
        image: Joi.string().uri().required(),
        description: Joi.string().default(""),
        link: Joi.string().uri().default(""),
      })
    )
    .default({}),
  testimonials: Joi.array()
    .items(
      Joi.object({
        title: Joi.string().required(),
        photo: Joi.string().uri().required(),
        video: Joi.string().default(""),
      })
    )
    .default([]),
});
// Add All the user routes here

router.post("/api/signup", async (req, res) => {
  console.log("New User Signup Request", req.body);
  const { name, email, password } = req.body;
  const hashedPassword = await bcrypt.hash(password, 10);

  // const rqObject = await prisma.requestLog.create({
  //   data: {
  //     url: req.url,
  //     method: req.method,
  //     headers: req.headers,
  //     body: req.body,
  //   },
  // });

  try {
    // give me a random 6 digit number
    const number = Math.floor(100000 + Math.random() * 900000);
    await prisma.user.create({
      data: {
        name,
        email,
        password: hashedPassword,
        userType: "client",
        slug: `${name}-${number}`.replace(/\s/g, "-").toLowerCase(),
      },
    });

    // await prisma.requestLog.update({
    //   where: {
    //     id: rqObject.id,
    //   },
    //   data: {
    //     response: { message: "User created successfully" },
    //     statusCode: 201,
    //     updatedAt: new Date(),
    //   },
    // });

    res.status(201).json({ message: "User created successfully" });
  } catch (error) {
    console.error("Error Stack:", error.stack);
    console.error("Error creating user:", error.message);
    Sentry.captureException(`Error creating user: ${error.message}`);

    // await prisma.requestLog.update({
    //   where: {
    //     id: rqObject.id,
    //   },
    //   data: {
    //     response: { error: "Internal server error" },
    //     statusCode: 500,
    //     updatedAt: new Date(),
    //   },
    // });

    res.status(500).json({ error: "Internal server error" });
  }
});

router.post("/api/login", async (req, res) => {
  const { email, password } = req.body;

  // const rqObject = await prisma.requestLog.create({
  //   data: {
  //     url: req.url,
  //     method: req.method,
  //     headers: req.headers,
  //     body: req.body,
  //   },
  // });

  try {
    const user = await prisma.user.findUnique({
      where: {
        email,
      },
    });

    if (!user || !(await bcrypt.compare(password, user.password))) {
      // await prisma.requestLog.update({
      //   where: {
      //     id: rqObject.id,
      //   },
      //   data: {
      //     response: { error: "Invalid email or password" },
      //     statusCode: 401,
      //     updatedAt: new Date(),
      //   },
      // });

      return res.status(401).json({ error: "Invalid email or password" });
    }

    const accessToken = jwt.sign(
      { userId: user.id, userType: user.userType },
      JWT_SECRET,
      {
        expiresIn: "7d",
      }
    );
    
    // Make sure the token starts with Bearer
    const authToken = `Bearer ${accessToken}`;
    
    // Set both Authorization and authorization headers to ensure compatibility
    res.setHeader("Authorization", authToken);
    res.setHeader("authorization", authToken);
    res.setHeader("Set-Cookie", `token=${accessToken}; HttpOnly`);

    // Log successful login
    console.log(`User ${user.id} (${user.userType}) logged in successfully`);

    // await prisma.requestLog.update({
    //   where: {
    //     id: rqObject.id,
    //   },
    //   data: {
    //     response: user,
    //     statusCode: 200,
    //     updatedAt: new Date(),
    //   },
    // });

    // Remove password from response
    const { password: _, ...userWithoutPassword } = user;
    res.status(200).json(userWithoutPassword);
  } catch (error) {
    console.error("Error Stack:", error.stack);
    console.error("Error logging in user:", error.message);
    Sentry.captureException(`Error logging in user: ${error.message}`);

    // await prisma.requestLog.update({
    //   where: {
    //     id: rqObject.id,
    //   },
    //   data: {
    //     response: { error: "Internal server error" },
    //     statusCode: 500,
    //     updatedAt: new Date(),
    //   },
    // });

    res.status(500).json({ error: "Internal server error" });
  }
});

router.get("/api/me", userAuth, async (req, res) => {
  const userId = req.user.userId;

  // const rqObject = await prisma.requestLog.create({
  //   data: {
  //     url: req.url,
  //     method: req.method,
  //     headers: req.headers,
  //     body: req.body,
  //   },
  // });

  try {
    const user = await prisma.user.findUnique({
      where: {
        id: userId,
      }, include: {
        Configurations: true,
      }
    });

    if (!user) {
      // await prisma.requestLog.update({
      //   where: {
      //     id: rqObject.id,
      //   },
      //   data: {
      //     response: { error: "User not found" },
      //     statusCode: 404,
      //     updatedAt: new Date(),
      //   },
      // });

      return res.status(404).json({ error: "User not found" });
    }

    const { password, ...rest } = user;
    
    // Add null check before accessing Configurations[0]
    if (user.Configurations && user.Configurations.length > 0) {
        rest.singleEntryOpt = user.Configurations[0].singleEntryOpt;
    } else {
        rest.singleEntryOpt = false; // Default value if no configurations exist
    }

    // await prisma.requestLog.update({
    //   where: {
    //     id: rqObject.id,
    //   },
    //   data: {
    //     response: rest,
    //     statusCode: 200,
    //     updatedAt: new Date(),
    //   },
    // });

    res.status(200).json(rest);
  } catch (error) {
    console.error("Error Stack:", error.stack);
    console.error("Error fetching user:", error.message);
    // Sentry.captureException(`Error fetching user: ${error.message}`);

    // await prisma.requestLog.update({
    //   where: {
    //     id: rqObject.id,
    //   },
    //   data: {
    //     response: { error: "Internal server error" },
    //     statusCode: 500,
    //     updatedAt: new Date(),
    //   },
    // });

    res.status(500).json({ error: "Internal server error" });
  }
});

router.get("/api/clients", adminAuth, async (req, res) => {
  console.log(
    `[${new Date().toISOString()}] ${req.method} ${
      req.originalUrl
    } - Fetching all clients`
  );
  // const rqObject = await prisma.requestLog.create({
  //   data: {
  //     url: req.url,
  //     method: req.method,
  //     headers: req.headers,
  //     body: req.body,
  //   },
  // });
  try {
    const users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        slug: true,
      },
    });
    // await prisma.requestLog.update({
    //   where: {
    //     id: rqObject.id,
    //   },
    //   data: {
    //     response: users,
    //     statusCode: 200,
    //     updatedAt: new Date(),
    //   },
    // });
    res.status(200).json(users);
  } catch (error) {
    // await prisma.requestLog.update({
    //   where: {
    //     id: rqObject.id,
    //   },
    //   data: {
    //     response: { error: "Internal Server Error" },
    //     statusCode: 500,
    //     updatedAt: new Date(),
    //   },
    // });
    console.error(
      `[${new Date().toISOString()}] ${req.method} ${
        req.originalUrl
      } - Error fetching clients:`,
      error
    );
    res.status(500).json({ error: "Internal Server Error" });
  }
});

// Route to update user fields, only accessible by admins
router.post("/api/user/:id", adminAuth, async (req, res) => {
  const userId = req.params.id;
  const updateData = req.body;

  // Log the request details
  // const rqObject = await prisma.requestLog.create({
  //   data: {
  //     url: req.url,
  //     method: req.method,
  //     headers: req.headers,
  //     body: req.body,
  //   },
  // });

  try {
    // Validate the request body
    const { error, value } = userSchema.validate(updateData);
    if (error) {
      console.error("Error updating user:", {
        error: error.details[0].message,
      });
      // await prisma.requestLog.update({
      //   where: { id: rqObject.id },
      //   data: {
      //     response: { error: error.details[0].message },
      //     statusCode: 400,
      //     updatedAt: new Date(),
      //   },
      // });
      return res.status(400).json({ error: error.details[0].message });
    }

    // Find the user by ID
    let user = await prisma.user.findUnique({
      where: { id: parseInt(userId) },
    });

    if (!user) {
      // await prisma.requestLog.update({
      //   where: { id: rqObject.id },
      //   data: {
      //     response: { error: "User not found" },
      //     statusCode: 404,
      //     updatedAt: new Date(),
      //   },
      // });
      return res.status(404).json({ error: "User not found" });
    }

    // console.log({ updateData });

    // Update ctaLink
    if (updateData.ctaLink) {
      updateData.ctaLink = await createAndUpdateUTMUrl({
        url: updateData.ctaLink,
        clientName: user.name,
        type: "ctaLink",
      });
    }

    // Update caseStudies links
    if (updateData.caseStudies) {
      for (const [key, caseStudy] of Object.entries(updateData.caseStudies)) {
        if (caseStudy.link) {
          caseStudy.link = await createAndUpdateUTMUrl({
            url: caseStudy.link,
            clientName: user.name,
            type: "caseStudies",
          });
        }
      }
    }

    // Update testimonials video links
    if (updateData.testimonials) {
      if (Array.isArray(updateData.testimonials)) {
        // If testimonials is an array
        for (const [index, testimonial] of updateData.testimonials.entries()) {
          if (testimonial.video) {
            testimonial.video = await createAndUpdateUTMUrl({
              url: testimonial.video,
              uuid: `testimonials-${index}`,
              clientName: user.name,
              type: "testimonials",
              source: "web",
            });
          }
        }
      } else if (typeof updateData.testimonials === "object") {
        // If testimonials is an object
        let index = 0;
        for (const testimonial of Object.values(updateData.testimonials)) {
          if (testimonial.video) {
            testimonial.video = await createAndUpdateUTMUrl({
              url: testimonial.video,
              uuid: `testimonials-${index}`,
              clientName: user.name,
              type: "testimonials",
              source: "web",
            });
            index++; // Increment index for UUID uniqueness
          }
        }
      } else {
        console.error(
          "Unexpected testimonials format:",
          updateData.testimonials
        );
      }
    }

    // Update user fields in the database
    user = await prisma.user.update({
      where: { id: parseInt(userId) },
      data: updateData,
    });

    // Log the response details
    // await prisma.requestLog.update({
    //   where: { id: rqObject.id },
    //   data: {
    //     response: user,
    //     statusCode: 200,
    //     updatedAt: new Date(),
    //   },
    // });

    res.json(user);
  } catch (error) {
    console.error("Error updating user:", error);
    // await prisma.requestLog.update({
    //   where: { id: rqObject.id },
    //   data: {
    //     response: { error: "Internal server error" },
    //     statusCode: 500,
    //     updatedAt: new Date(),
    //   },
    // });
    res.status(500).json({ error: "Internal server error" });
  }
});

// Route to fetch user details by ID, only accessible by admins
router.get("/api/user/:id", adminAuth, async (req, res) => {
  const userId = parseInt(req.params.id);

  // const rqObject = await prisma.requestLog.create({
  //   data: {
  //     url: req.url,
  //     method: req.method,
  //     headers: req.headers,
  //     body: req.body,
  //   },
  // });

  try {
    const user = await prisma.user.findUnique({
      where: {
        id: userId,
      },
      select: {
        id: true,
        name: true,
        slug: true,
        website: true,
        offerLine:true,
        ctaButtonText:true,
        googleSheetURL:true,
        signature: true,
        logo: true,
        mbLogo: true,
        tableLogo: true,
        ctaLink: true,
        bgColor: true,
        caseStudies: true,
        caseStudiesText:true,
        testimonials: true,
        Configurations: true,
      },
    });

    if (!user) {
      // await prisma.requestLog.update({
      //   where: {
      //     id: rqObject.id,
      //   },
      //   data: {
      //     response: { error: "User not found" },
      //     statusCode: 404,
      //     updatedAt: new Date(),
      //   },
      // });

      return res.status(404).json({ error: "User not found" });
    }
    // await prisma.requestLog.update({
    //   where: {
    //     id: rqObject.id,
    //   },
    //   data: {
    //     response: user,
    //     statusCode: 200,
    //     updatedAt: new Date(),
    //   },
    // });

    res.status(200).json(user);
  } catch (error) {
    console.error("Error Stack:", error.stack);
    console.error("Error fetching user:", error.message);
    // Sentry.captureException(`Error fetching user: ${error.message}`);

    // await prisma.requestLog.update({
    //   where: {
    //     id: rqObject.id,
    //   },
    //   data: {
    //     response: { error: "Internal server error" },
    //     statusCode: 500,
    //     updatedAt: new Date(),
    //   },
    // });

    res.status(500).json({ error: "Internal server error" });
  }
});

router.post("/api/user-logout", userAuth, async (req, res) => {
  try {
    res.setHeader("Set-Cookie", "token=; HttpOnly; Max-Age=0");
    res.status(200).json({ message: "Logged out successfully" });
  } catch (error) {
    console.error("Error logging out user:", error.message);
    Sentry.captureException(`Error logging out user: ${error.message}`);
    res.status(500).json({ error: "Internal server error" });
  }
});

module.exports = router;
