const express = require("express");
const router = express.Router();
const prisma = require("../database/prisma/getPrismaClient");
const Joi = require("joi");
const { adminAuth } = require("../middlewares/jwt");

// Define the schema for validation
const configSchema = Joi.object({
  clientId: Joi.number().integer().required(),
  scraperApi: Joi.object({
    SCRAPER_API_MAX_RETRIES: Joi.number().integer().required(),
    SCRAPER_API_HOST: Joi.string().required(),
    SCRAPER_API_PORT: Joi.number().integer().required(),
    SCRAPER_API_USERNAME: Joi.string().required(),
    SCRAPER_API_KEY: Joi.string().required(),
  }).default("{}"),
  scrapingBeeApi: Joi.object({
    SCRAPING_BEE_API_KEY: Joi.string().required(),
  }).default("{}"),
  jungleScout: Joi.object({
    apiKey: Joi.string().required(),
    keyName: Joi.string().required(),
    baseURL: Joi.string().uri().required(),
  }).default("{}"),
  openAi: Joi.object({
    apiKey: Joi.string().required(),
    assistantId: Joi.string().required(),
    modelId: Joi.string().required(),
  }).default("{}"),
  compEmailTemplates: Joi.object({
    completed: Joi.object({
      haveRevenue: Joi.string().required(),
      noRevenue: Joi.string().required(),
    }).required(),
  }).default("{}"),
  promptTemplates: Joi.object({
    compSearchKeyword: Joi.string(),
    caseStudies: Joi.string().required(),
    bsrCategoryMatch: Joi.string().required(),
    ppcAudit: Joi.string(),
    bsrCategoryUsingGpt: Joi.string(),
    mainImageOptimisation: Joi.string(),
    productTitleHumanisation: Joi.string(),
    companyNameHumanisation: Joi.string(),
  }).default("{}"),
  singleEntryOpt: Joi.bool().required(),
});

// POST endpoint to create or update configuration data
router.post("/api/configurations", adminAuth, async (req, res) => {
  const { error, value } = configSchema.validate(req.body);

  if (error) {
    return res.status(400).json({ error: error.details[0].message });
  }

  const { clientId, scraperApi,scrapingBeeApi, jungleScout, openAi, compEmailTemplates, promptTemplates,singleEntryOpt } =
  req.body;

  try {
    // If the configuration already exists, update it
    let existingConfig = await prisma.configurations.findFirst({
      where: {
        clientId,
      },
    });

    if (existingConfig) {
      const updatedConfig = await prisma.configurations.update({
        where: {
          id: existingConfig.id,
        },
        data: {
          scraperApi,
          scrapingBeeApi,
          jungleScout,
          openAi,
          compEmailTemplates,
          promptTemplates,
          singleEntryOpt
        },
      });
      return res.status(200).json(updatedConfig);
    } else {
      const client = await prisma.user.findUnique({
        where: {
          id: clientId,
        },
      });
      if (!client) {
        return res.status(404).json({ error: "Client not found" });
      }
      existingConfig = await prisma.configurations.create({
        data: {
          clientId,
          scraperApi,
          scrapingBeeApi,
          jungleScout,
          openAi,
          compEmailTemplates,
          promptTemplates,
          singleEntryOpt
        },
      });
    }

    res.status(201).json(existingConfig);
  } catch (error) {
    console.error("Error Stack:", error.stack);
    console.error("Error creating configuration:", error.message);
    res.status(500).json({ error: "Internal server error" });
  }
});

module.exports = router;
