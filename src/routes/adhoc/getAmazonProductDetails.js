const express = require("express");
const router = express.Router();
const prisma = require("../../database/prisma/getPrismaClient");
const {
  getProductDetails,
} = require("../../utils/scrapeAmazonUtils/getProductDetails");
const { getHtmlByProxy } = require("../../utils/getHtmlByProxy");

/**
 * Route: POST /api/adhoc/get-amazon-product-detail
 * Description: Fetches product details from Amazon for a given product URL.
 * Request Body:
 *   - productUrl (string): The URL of the Amazon product to scrape.
 * Responses:
 *   - 200 OK: Returns the scraped product details.
 *   - 400 Bad Request: If the productUrl is missing or invalid.
 *   - 404 Not Found: If the product was not found or could not be scraped.
 *   - 500 Internal Server Error: On unexpected server errors.
 */
router.post("/api/adhoc/get-amazon-product-detail", async (req, res) => {
  try {
    const { productUrl } = req.body;

    // Validate productUrl
    if (!productUrl || typeof productUrl !== "string") {
      return res
        .status(400)
        .json({ error: "Invalid or missing 'productUrl'." });
    }
    const htmlData = await getHtmlByProxy(productUrl, 1);

    const productDetails = await getProductDetails(
      htmlData,
      productUrl,
      productUrl
    );

    if (!productDetails) {
      return res
        .status(404)
        .json({ error: "Product not found or could not be scraped." });
    }

    // Remove company_name
    const { company_name, ...rest } = productDetails;

    res.status(200).json(rest);
  } catch (error) {
    console.error("Error fetching product data:", error);
    res.status(500).json({ error: "Internal server error." });
  }
});


module.exports = router;
