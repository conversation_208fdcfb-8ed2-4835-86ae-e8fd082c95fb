const awsInstanceManager = require('../services/aws/awsInstanceManager');

// List all instances
const listInstances = async (req, res) => {
  try {
    const instances = await awsInstanceManager.listInstances();
    return res.status(200).json({ 
      success: true, 
      data: instances 
    });
  } catch (error) {
    console.error('Error in listInstances controller:', error);
    
    // Check for specific error types
    if (error.message.includes('credentials missing')) {
      return res.status(503).json({ 
        success: false, 
        error: 'AWS credentials are not configured properly. Please contact the administrator.' 
      });
    }
    
    if (error.message.includes('Region is missing')) {
      return res.status(503).json({ 
        success: false, 
        error: 'AWS region is not configured properly. Please contact the administrator.' 
      });
    }
    
    if (error.message.includes('permission denied') || error.message.includes('not authorized')) {
      return res.status(403).json({ 
        success: false, 
        error: error.message,
        recommendation: 'Your AWS user needs EC2 permissions. Please contact your AWS administrator to add the AmazonEC2FullAccess policy to your user.'
      });
    }
    
    return res.status(500).json({ 
      success: false, 
      error: error.message || 'Failed to list instances' 
    });
  }
};

// Start an instance
const startInstance = async (req, res) => {
  try {
    // Use either instanceId or id parameter
    const instanceId = req.params.instanceId || req.params.id;
    
    if (!instanceId) {
      return res.status(400).json({ 
        success: false, 
        error: 'Instance ID is required' 
      });
    }
    
    const result = await awsInstanceManager.startInstance(instanceId);
    return res.status(200).json({ 
      success: result.success, 
      message: result.message,
      deploymentDetails: result.deploymentDetails
    });
  } catch (error) {
    console.error('Error in startInstance controller:', error);
    return res.status(500).json({ 
      success: false, 
      error: error.message || 'Failed to start instance' 
    });
  }
};

// Stop an instance
const stopInstance = async (req, res) => {
  try {
    // Use either instanceId or id parameter
    const instanceId = req.params.instanceId || req.params.id;
    
    if (!instanceId) {
      return res.status(400).json({ 
        success: false, 
        error: 'Instance ID is required' 
      });
    }
    
    const result = await awsInstanceManager.stopInstance(instanceId);
    return res.status(200).json({ 
      success: result.success, 
      message: result.message 
    });
  } catch (error) {
    console.error('Error in stopInstance controller:', error);
    return res.status(500).json({ 
      success: false, 
      error: error.message || 'Failed to stop instance' 
    });
  }
};

// Reboot an instance
const rebootInstance = async (req, res) => {
  try {
    // Use either instanceId or id parameter
    const instanceId = req.params.instanceId || req.params.id;
    
    if (!instanceId) {
      return res.status(400).json({ 
        success: false, 
        error: 'Instance ID is required' 
      });
    }
    
    const result = await awsInstanceManager.rebootInstance(instanceId);
    return res.status(200).json({ 
      success: result.success, 
      message: result.message,
      deploymentDetails: result.deploymentDetails
    });
  } catch (error) {
    console.error('Error in rebootInstance controller:', error);
    return res.status(500).json({ 
      success: false, 
      error: error.message || 'Failed to reboot instance' 
    });
  }
};


module.exports = {
  listInstances,
  startInstance,
  stopInstance,
  rebootInstance,
}; 