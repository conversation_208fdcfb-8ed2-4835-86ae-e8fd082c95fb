# AmazonProductData Sync Worker

This worker automatically syncs AmazonProductData records to the target database every 6 hours.

## Features

- 🔄 **Automatic Sync**: Runs every 6 hours via cron job
- 📊 **Smart Filtering**: Only syncs records that haven't been pushed yet (`pushedToSB: false`)
- ⚡ **Batch Processing**: Processes records in configurable batches
- 📝 **History Tracking**: Always creates new rows to track product data changes over time
- ✅ **Validation**: Validates data before syncing
- 📈 **Detailed Logging**: Comprehensive logs with emojis for easy reading

## Configuration

### Environment Variables

Add these to your `.env` file:

```bash
# Enable Amazon sync worker
ENABLE_AMAZON_SYNC=true

# Target database configuration
TARGET_DB_HOST=localhost
TARGET_DB_PORT=5430
TARGET_DB_NAME=sellers-db
TARGET_DB_USER=mukul
TARGET_DB_PASSWORD=5662
TARGET_DB_SSL=false

# Migration configuration
MIGRATION_BATCH_SIZE=50
MIGRATION_SKIP_EXISTING=false
MIGRATION_UPDATE_EXISTING=true
```

### Configuration Options

| Variable | Default | Description |
|----------|---------|-------------|
| `ENABLE_AMAZON_SYNC` | `false` | Enable/disable the sync worker |
| `TARGET_DB_HOST` | `localhost` | Target database host |
| `TARGET_DB_PORT` | `5432` | Target database port |
| `TARGET_DB_NAME` | `sellers-db` | Target database name |
| `TARGET_DB_USER` | `mukul` | Target database username |
| `TARGET_DB_PASSWORD` | `5662` | Target database password |
| `TARGET_DB_SSL` | `false` | Enable SSL for target database |
| `MIGRATION_BATCH_SIZE` | `50` | Number of records to process per batch |
| `MIGRATION_SKIP_EXISTING` | `false` | Not used - always creates new rows |
| `MIGRATION_UPDATE_EXISTING` | `false` | Not used - always creates new rows |

## Usage

### 1. Run as Standalone Worker

```bash
npm run sync:amazon
```

This will:
- Run an initial sync immediately
- Start the cron job to run every 6 hours
- Keep running until manually stopped

### 2. Run with Main Application

The worker is automatically started when the main application runs if `ENABLE_AMAZON_SYNC=true` is set.

### 3. Manual Sync

You can also run a one-time sync:

```javascript
const { syncAmazonProductData } = require('./src/worker/amazonProductDataSyncWorker');

// Run sync manually
await syncAmazonProductData();
```

## Cron Schedule

The worker runs every 6 hours using the cron expression: `0 */6 * * *`

- **0** - At minute 0
- **\*/6** - Every 6 hours
- **\* \* \*** - Every day, every month, every day of week

## Data Flow

1. **Fetch Records**: Gets all AmazonProductData where `pushedToSB: false`
2. **Transform Data**: Maps AmazonProductData fields to Product table schema
3. **Validate**: Checks for required fields and valid URLs
4. **Insert New Row**: Always creates a new row in the target database
6. **Mark Synced**: Sets `pushedToSB: true` for successfully synced records

## Field Mapping

The worker maps AmazonProductData fields to the Product table:

| Product Field | AmazonProductData Source |
|---------------|-------------------------|
| `url` | `data.productData[0].url` |
| `brand_name` | `data.company_name` |
| `product_title` | `data.productData[0].productTitle.value` |
| `description` | `data.productData[0].description` |
| `price` | `data.productData[0].price` |
| `rating` | `data.productData[0].rating.rating` |
| `total_reviews` | `data.productData[0].review.totalReviewCountInt` |
| `star_X_count` | `data.productData[0].review.reviewPerStar` |
| `main_image_url` | `data.productData[0].productMainImage` |
| `secondary_images` | `data.productData[0].secondaryImages` |

## Logging

The worker provides detailed logging with emojis:

- 🔄 Starting sync job
- 📊 Found X records to sync
- 📦 Processing batch X/Y
- ✅ Successfully synced (new row created)
- ⚠️ Validation errors
- ❌ Database errors
- 📈 Sync summary

## Error Handling

- **Validation Errors**: Records with critical errors are skipped
- **Database Errors**: Individual record failures don't stop the entire sync
- **Connection Errors**: Automatic retry and graceful shutdown
- **Graceful Shutdown**: Handles SIGINT and SIGTERM signals

## Monitoring

Check the logs to monitor sync status:

```bash
# View recent logs
tail -f logs/app.log | grep "AmazonProductData"

# Check sync summary
grep "Sync Summary" logs/app.log
```

## Troubleshooting

### Common Issues

1. **Target table doesn't exist**
   - Ensure the `products` table exists in the target database
   - Run the migration to create the table

2. **Connection errors**
   - Check target database credentials
   - Verify network connectivity
   - Check SSL configuration

3. **No records syncing**
   - Check if records have `pushedToSB: false`
   - Verify the `pushedToSB` field exists in AmazonProductData table

4. **Validation errors**
   - Check source data quality
   - Verify required fields are present
   - Check URL format

### Debug Mode

Enable debug logging by setting:

```bash
DEBUG=amazon-sync:*
```

## Performance

- **Batch Size**: Default 50 records per batch
- **Memory Usage**: Minimal, processes in batches
- **Database Load**: Spreads load with batching
- **Network**: Efficient connection reuse

## Security

- Database credentials stored in environment variables
- SSL support for secure connections
- No sensitive data logged
- Graceful error handling without exposing internals 