const prisma = require("../database/prisma/getPrismaClient");
const cron = require("node-cron");
const { ReviewCheckerStatus } = require("@prisma/client");
const { processReview, sendLexSlackNotification } = require("../services/lex/reviewChecker/utils");
const ResurrectedReviewHandler = require("../services/lex/reviewChecker/resurrectedReviewHandler");
require("dotenv").config();

const Bottleneck = require("bottleneck");

// Configure Bottleneck for concurrency and rate limiting
const limiter = new Bottleneck({
    maxConcurrent: 40, // Number of concurrent requests
    minTime: 100, // Minimum time between each request (ms)
});

//every day at 12:00 AM
cron.schedule(
    "0 21 * * *",
    async () => {
        try {
            await reviewChecker();
        } catch (error) {
            console.error("Error in scheduled review check:", error);
            await sendLexSlackNotification(
                null,
                "PING",
                "❌ Error in daily review check: " + error.message
            );
        }
    },
    {
        timezone: "Asia/Kolkata", // or your desired timezone
        scheduled: true,
        runOnInit: false, // Set to true if you want it to run immediately when the server starts
    }
);

async function reviewChecker() {
    console.log("Starting daily review check...");

    try {
        // Calculate the last minute of today
        const lastTimestampToday = new Date();
        lastTimestampToday.setHours(23, 59, 59, 999);

        const today = new Date();
        today.setHours(0, 0, 0, 0);

        let numOfFailedChecks = 0

        // Fetch reviews that are due for checking based on next_run date
        const reviews = await prisma.lexReview.findMany({
            where: {
                checkerStatus: {
                    in: [ReviewCheckerStatus.PRESENT, ReviewCheckerStatus.FAILED],
                },
                next_run: {
                    lte: lastTimestampToday,
                },
            },
            orderBy: [
                { next_run: "asc" }, // Process the oldest scheduled reviews first
                { updatedAt: "asc" }, // Then by last update time for those without next_run
            ],
        });

        console.log(`Found ${reviews.length} reviews due for checking.`);
        await sendLexSlackNotification(
            null,
            "PING",
            "Starting daily review check...",
            reviews.length
        );
        const removedReviews = [];

        const tasks = reviews.map((review) =>
            limiter.schedule(async () => {
                const reviewCheck = await prisma.lexReview.findUnique({
                    where: {
                        id: review.id,
                    },
                    select: {
                        next_run: true,
                    },
                });

                if (reviewCheck.next_run && reviewCheck.next_run > lastTimestampToday) {
                    console.log(`Skipping review ${review.id}, already checked today.`);
                    return;
                }
                try {
                    const rawReviewStatus = await processReview(review.reviewLink);

                    // Map string values to ReviewCheckerStatus
                    let newStatus;
                    if (rawReviewStatus === "REMOVED") {
                        newStatus = ReviewCheckerStatus.REMOVED;
                    } else if (rawReviewStatus === "PRESENT") {
                        newStatus = ReviewCheckerStatus.PRESENT;
                    } else {
                        newStatus = ReviewCheckerStatus.FAILED;
                    }

                    // Check if status transition is needed for resurrected reviews
                    const updatedReview = await ResurrectedReviewHandler.checkStatusTransition(review, newStatus);

                    // Calculate next run date based on run_frequency
                    const nextRunDate = new Date();
                    // Use the run_frequency if available, otherwise default to 7 days
                    const frequency = review.run_frequency || 7;
                    nextRunDate.setDate(nextRunDate.getDate() + frequency);

                    // Update review with new status and scheduling info
                    await prisma.lexReview.update({
                        where: { id: review.id },
                        data: {
                            checkerStatus: updatedReview.status,
                            next_run: nextRunDate,
                            // Increment totalRuns
                            totalRuns: review.totalRuns + 1,
                            removedAt: updatedReview.removedAt,
                            removedHistory: updatedReview.removedHistory || review.removedHistory,
                            returnedHistory: updatedReview.returnedHistory || review.returnedHistory,
                            comments: updatedReview.comments || review.comments,
                        },
                    });

                    console.log(
                        `Checked review ${review.id}: ${newStatus}, next check scheduled for ${nextRunDate.toISOString()}`
                    );
                    if (
                        updatedReview.status === ReviewCheckerStatus.REMOVED &&
                        review.checkerStatus === ReviewCheckerStatus.PRESENT
                    ) {
                        removedReviews.push(updatedReview);
                    }
                } catch (error) {
                    console.error(`Error checking review ${review.id}:`, error.message);

                    // For failed reviews, schedule a retry in 1 day
                    const retryDate = new Date();
                    retryDate.setDate(retryDate.getDate() + 1);

                    // Update status to FAILED and set next_run to retry in 1 hour
                    await prisma.lexReview
                        .update({
                            where: { id: review.id },
                            data: {
                                checkerStatus: ReviewCheckerStatus.FAILED,
                                next_run: retryDate,
                                // Increment totalRuns
                                totalRuns: review.totalRuns + 1,
                            },
                        })
                        .catch((err) => {
                            console.error("Error updating review status:", err.message);
                        });
                    numOfFailedChecks++;

                    console.log(
                        `Failed to check review ${review.id}, scheduled retry for ${retryDate.toISOString()}`
                    );
                }
            })
        );

        await Promise.all(tasks);

        await sendLexSlackNotification(
            removedReviews,
            "DAILY",
            null,
            reviews.length,
            numOfFailedChecks
        );
        console.log("Daily review check completed.");

        await sendLexSlackNotification(
            null,
            "PING",
            "Daily review check completed successfully!",
            reviews.length
        );
    } catch (error) {
        console.error("Error during daily review check:", error.message);
    }
}
if (require.main === module) {
    reviewChecker();
}
module.exports = { reviewChecker };
