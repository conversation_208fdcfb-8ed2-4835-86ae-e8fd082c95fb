const express = require("express");
const cors = require("cors");
const  config = require("./src/config");
const schedule = require("./src/worker/scheduler");
const newrelic = require('newrelic');
const prisma = require("./src/database/prisma/getPrismaClient");
require("dotenv").config();



const app = express();
const PORT = 8000;

// Middleware
app.use(
  cors({
    origin: "*",
    methods: "GET,HEAD,PUT,PATCH,POST,DELETE",
    preflightContinue: false,
    optionsSuccessStatus: 204,
    exposedHeaders: ["Content-Type", "Authorization", "Content-Disposition"],
  })
);
app.use(express.json());

// Import routes
const routes = require("./src/routes");
app.use(routes);

app.listen(PORT, () =>
  console.log(`Server running on http://localhost:${PORT}`)
);